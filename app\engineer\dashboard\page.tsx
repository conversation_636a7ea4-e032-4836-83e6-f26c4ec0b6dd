"use client"

import { useState } from "react"
import { Header } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { StatsCard } from "@/components/dashboard/stats-card"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Ticket,
  CheckCircle,
  Timer,
  Zap,
  MapPin,
  Wrench,
  Clock,
  Play,
  ArrowRight,
  Calendar,
  Info,
  Phone,
  MessageSquare,
  CheckSquare,
  FileText,
  Smartphone,
  Navigation,
  AlertTriangle,
  Settings,
  MoreHorizontal,
  Eye,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function EngineerDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const activeTickets = {
    urgent: [
      {
        id: "T-001",
        title: "FASTag Reader Critical Failure",
        location: "South City Mall",
        area: "Entry Gate 1",
        eta: "30 min",
        priority: "urgent",
        description: "Complete system failure, temperature rising rapidly",
      },
    ],
    medium: [
      {
        id: "T-002",
        title: "ANPR Camera Maintenance",
        location: "City Center Mall",
        area: "Exit Gate 2",
        eta: "2 hours",
        priority: "medium",
        description: "Scheduled maintenance and safety inspection",
      },
    ],
    low: [
      {
        id: "T-003",
        title: "Ticket Dispenser Paper Refill",
        location: "Mani Casadona IT Park",
        area: "Visitor Parking",
        eta: "Tomorrow",
        priority: "low",
        description: "Replace fluorescent bulbs in reception area",
      },
    ],
  }

  const tools = [
    { name: "FASTag System Check", completed: true },
    { name: "ANPR Camera Calibration", completed: true },
    { name: "Ticket Dispenser Maintenance", completed: false },
    { name: "RFID Gate Testing", completed: true },
    { name: "UHF Antenna Alignment", completed: false },
    { name: "PGS Sensor Cleaning", completed: false },
  ]

  const quickContacts = [
    { name: "Emergency", number: "911", type: "emergency" },
    { name: "Supervisor", number: "ext. 234", type: "supervisor" },
    { name: "Parts Request", number: "ext. 567", type: "parts" },
    { name: "Safety Hotline", number: "ext. 890", type: "safety" },
  ]

  const currentLocation = {
    name: "South City Mall",
    address: "123 Medical Drive, East Zone",
    nextStop: "City Center Mall",
    travelTime: "15 minutes",
    route: "Route A-3",
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "border-red-200 bg-red-50"
      case "medium":
        return "border-yellow-200 bg-yellow-50"
      case "low":
        return "border-green-200 bg-green-50"
      default:
        return "border-gray-200 bg-gray-50"
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-500 text-white"
      case "medium":
        return "bg-yellow-500 text-white"
      case "low":
        return "bg-green-500 text-white"
      default:
        return "bg-gray-500 text-white"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar role="engineer" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="flex-1 flex flex-col min-w-0">
        <Header title="Engineer Dashboard" onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 p-6 overflow-auto">
          {/* Engineer Header */}
          <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-6 text-white mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">👨‍🔧 Vinod - South City Mall Engineer</h1>
                <p className="text-green-100">Status: 🟢 Active | Location: {currentLocation.name}</p>
              </div>
              <div className="flex space-x-2">
                <Button variant="secondary" className="bg-white text-green-600 hover:bg-gray-100">
                  🔄 Sync
                </Button>
                <Button variant="secondary" className="bg-white text-green-600 hover:bg-gray-100">
                  📍 GPS
                </Button>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <StatsCard title="My Tasks" value="8" change="📈 +2 today" changeType="positive" icon={Ticket} />
            <StatsCard title="Done Today" value="3" change="📊 +1 completed" changeType="positive" icon={CheckCircle} />
            <StatsCard
              title="Avg Time Per Ticket"
              value="1.2 hrs"
              change="📉 -0.3 improved"
              changeType="positive"
              icon={Timer}
            />
          </div>

          {/* Quick Actions */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="mr-2 h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-3">
                <Button className="flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  Clock In/Out
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Ticket className="mr-2 h-4 w-4" />
                  Create Ticket
                </Button>
                <Button variant="destructive" className="flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Emergency
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* My Active Tickets */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Ticket className="mr-2 h-5 w-5" />
                  My Active Tickets (Priority Order)
                </div>
                <Button variant="outline" size="sm">
                  <Eye className="mr-2 h-4 w-4" />
                  View All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Urgent */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-red-700">🔴 URGENT</h3>
                    <Badge variant="destructive">{activeTickets.urgent.length}</Badge>
                  </div>
                  {activeTickets.urgent.map((ticket) => (
                    <div key={ticket.id} className={`border rounded-lg p-4 ${getPriorityColor(ticket.priority)}`}>
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <Badge className={`mb-2 ${getPriorityBadge(ticket.priority)}`}>URGENT</Badge>
                          <h4 className="font-semibold mb-1 truncate">{ticket.title}</h4>
                          <p className="text-sm text-gray-600 truncate">{ticket.location}</p>
                          <p className="text-sm text-gray-600 truncate">{ticket.area}</p>
                          <p className="text-sm font-medium mt-2">ETA: {ticket.eta}</p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="flex-shrink-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Navigation className="mr-2 h-4 w-4" />
                              Get Directions
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Phone className="mr-2 h-4 w-4" />
                              Call Support
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <div className="space-y-2">
                        <Button size="sm" className="w-full">
                          🚀 Start Now
                        </Button>
                        <Button size="sm" variant="outline" className="w-full">
                          <Phone className="mr-1 h-3 w-3" />
                          Call Manager
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Medium */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-yellow-700">🟡 MEDIUM</h3>
                    <Badge className="bg-yellow-500">{activeTickets.medium.length}</Badge>
                  </div>
                  {activeTickets.medium.map((ticket) => (
                    <div key={ticket.id} className={`border rounded-lg p-4 ${getPriorityColor(ticket.priority)}`}>
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <Badge className={`mb-2 ${getPriorityBadge(ticket.priority)}`}>MEDIUM</Badge>
                          <h4 className="font-semibold mb-1 truncate">{ticket.title}</h4>
                          <p className="text-sm text-gray-600 truncate">{ticket.location}</p>
                          <p className="text-sm text-gray-600 truncate">{ticket.area}</p>
                          <p className="text-sm font-medium mt-2">ETA: {ticket.eta}</p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="flex-shrink-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Play className="mr-2 h-4 w-4" />
                              Continue Work
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <MessageSquare className="mr-2 h-4 w-4" />
                              Update Status
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <div className="space-y-2">
                        <Button size="sm" variant="outline" className="w-full">
                          <Play className="mr-1 h-3 w-3" />
                          Continue
                        </Button>
                        <Button size="sm" variant="outline" className="w-full">
                          📊 Progress
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Low */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-green-700">🟢 LOW</h3>
                    <Badge className="bg-green-500">{activeTickets.low.length}</Badge>
                  </div>
                  {activeTickets.low.map((ticket) => (
                    <div key={ticket.id} className={`border rounded-lg p-4 ${getPriorityColor(ticket.priority)}`}>
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <Badge className={`mb-2 ${getPriorityBadge(ticket.priority)}`}>LOW</Badge>
                          <h4 className="font-semibold mb-1 truncate">{ticket.title}</h4>
                          <p className="text-sm text-gray-600 truncate">{ticket.location}</p>
                          <p className="text-sm text-gray-600 truncate">{ticket.area}</p>
                          <p className="text-sm font-medium mt-2">ETA: {ticket.eta}</p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="flex-shrink-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem>
                              <Info className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Calendar className="mr-2 h-4 w-4" />
                              Reschedule
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <MessageSquare className="mr-2 h-4 w-4" />
                              Contact Client
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <div className="space-y-2">
                        <Button size="sm" variant="outline" className="w-full">
                          <Calendar className="mr-1 h-3 w-3" />
                          Schedule
                        </Button>
                        <Button size="sm" variant="outline" className="w-full">
                          <Info className="mr-1 h-3 w-3" />
                          Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Interactive Zone Map */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MapPin className="mr-2 h-5 w-5" />
                    Interactive Zone Map & Route
                  </div>
                  <Button variant="outline" size="sm">
                    <Navigation className="mr-2 h-4 w-4" />
                    Full Map
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-lg p-6 text-center mb-4">
                  <MapPin className="mx-auto h-12 w-12 text-blue-500 mb-4" />
                  <h3 className="font-semibold text-lg mb-2">Current Route Overview</h3>
                  <p className="text-gray-600 mb-4">Real-time location and navigation assistance</p>
                </div>

                <div className="space-y-4">
                  <div className="border rounded-lg p-4 bg-blue-50">
                    <h4 className="font-semibold mb-2 flex items-center">
                      <MapPin className="mr-2 h-4 w-4 text-blue-600" />
                      Current Location
                    </h4>
                    <p className="text-sm font-medium">{currentLocation.name}</p>
                    <p className="text-xs text-gray-600">{currentLocation.address}</p>
                  </div>

                  <div className="border rounded-lg p-4 bg-green-50">
                    <h4 className="font-semibold mb-2 flex items-center">
                      <ArrowRight className="mr-2 h-4 w-4 text-green-600" />
                      Next Destination
                    </h4>
                    <p className="text-sm font-medium">{currentLocation.nextStop}</p>
                    <p className="text-xs text-gray-600">Travel time: {currentLocation.travelTime}</p>
                    <p className="text-xs text-gray-600">Route: {currentLocation.route}</p>
                  </div>

                  <div className="flex space-x-2">
                    <Button className="flex-1">
                      <Navigation className="mr-2 h-4 w-4" />
                      Get Directions
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <Phone className="mr-2 h-4 w-4" />
                      Call Dispatch
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tools & Resources */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Wrench className="mr-2 h-5 w-5" />
                    Tools & Resources
                  </div>
                  <Button variant="outline" size="sm">
                    <Settings className="mr-2 h-4 w-4" />
                    Manage
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Digital Checklists */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3 flex items-center">
                      <CheckSquare className="mr-2 h-4 w-4" />
                      Digital Checklists
                    </h4>
                    <div className="space-y-2">
                      {tools.map((tool, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckSquare className={`h-4 w-4 ${tool.completed ? "text-green-600" : "text-gray-400"}`} />
                          <span className={`text-sm ${tool.completed ? "text-green-700" : "text-gray-600"}`}>
                            {tool.name}
                          </span>
                          {tool.completed && <Badge className="bg-green-100 text-green-800 text-xs">✓</Badge>}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Mobile Tools */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Smartphone className="mr-2 h-4 w-4" />
                      Mobile Tools
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Button size="sm" variant="outline" className="text-xs">
                        <Smartphone className="mr-1 h-3 w-3" />
                        Scanner
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <FileText className="mr-1 h-3 w-3" />
                        Reports
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <MessageSquare className="mr-1 h-3 w-3" />
                        Chat
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <Phone className="mr-1 h-3 w-3" />
                        Support
                      </Button>
                    </div>
                  </div>

                  {/* Quick Reference */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3 flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      Quick Reference
                    </h4>
                    <div className="space-y-2">
                      {quickContacts.map((contact, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">• {contact.name}:</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium">{contact.number}</span>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <Phone className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Today's Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Calendar className="mr-2 h-5 w-5" />
                  Today's Schedule & Progress
                </div>
                <Button variant="outline" size="sm">
                  <Eye className="mr-2 h-4 w-4" />
                  View Week
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">08:00 - 10:00</p>
                      <p className="text-sm text-gray-600">FASTag System Check - South City Mall</p>
                    </div>
                  </div>
                  <Badge className="bg-green-500">Completed</Badge>
                </div>

                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                    <div>
                      <p className="font-medium">10:30 - 12:00</p>
                      <p className="text-sm text-gray-600">ANPR Camera Service - City Center Mall</p>
                    </div>
                  </div>
                  <Badge className="bg-blue-500">In Progress</Badge>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <div>
                      <p className="font-medium">14:00 - 15:00</p>
                      <p className="text-sm text-gray-600">Preventive Maintenance - Manipal Hospital</p>
                    </div>
                  </div>
                  <Badge variant="outline">Scheduled</Badge>
                </div>

                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold mb-2">Daily Progress</h4>
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span>Tasks Completed</span>
                    <span>3 of 8 (37.5%)</span>
                  </div>
                  <Progress value={37.5} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
