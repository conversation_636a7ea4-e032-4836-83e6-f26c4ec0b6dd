"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { StatsCard } from "@/components/dashboard/stats-card"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Ticket,
  Activity,
  CheckCircle,
  AlertTriangle,
  Users,
  TrendingUp,
  MapPin,
  Zap,
  BarChart3,
  Settings,
  MessageSquare,
} from "lucide-react"

export default function ZaraDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar role="zara" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="lg:ml-64">
        <Header title="AI SuperAdmin Dashboard" onMenuClick={() => setSidebarOpen(true)} />

        <main className="p-6">
          {/* Welcome Banner */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">Welcome back, Zara! 🚀 ParkWiz AI SuperAdmin</h1>
                <p className="text-blue-100">System Status: All Green ✅</p>
              </div>
              <Button variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
                Emergency 🚨
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
            <StatsCard title="Total Tickets" value="1,247" change="+5% ↗️" changeType="positive" icon={Ticket} />
            <StatsCard title="Active Tickets" value="89" change="+12% ↗️" changeType="positive" icon={Activity} />
            <StatsCard title="Today Resolved" value="45" change="+8% ↗️" changeType="positive" icon={CheckCircle} />
            <StatsCard title="Overdue" value="12" change="-3% ↘️" changeType="positive" icon={AlertTriangle} />
            <StatsCard title="Online Engineers" value="12/15" change="80%" changeType="neutral" icon={Users} />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Recent Critical Tickets */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Ticket className="mr-2 h-5 w-5" />
                  Recent Critical Tickets
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border border-red-200 rounded-lg p-4 bg-red-50">
                  <div className="flex items-start justify-between">
                    <div>
                      <Badge variant="destructive" className="mb-2">
                        URGENT
                      </Badge>
                      <h4 className="font-semibold">FASTag Reader Malfunction - South City Mall</h4>
                      <p className="text-sm text-gray-600">South City Mall</p>
                      <p className="text-xs text-gray-500">2 min ago | Unassigned</p>
                    </div>
                    <div className="space-x-2">
                      <Button size="sm">Assign Now</Button>
                      <Button size="sm" variant="outline">
                        Details
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                  <div className="flex items-start justify-between">
                    <div>
                      <Badge variant="secondary" className="mb-2 bg-yellow-500 text-white">
                        HIGH
                      </Badge>
                      <h4 className="font-semibold">ANPR Camera Offline - City Center Mall</h4>
                      <p className="text-sm text-gray-600">City Center Mall</p>
                      <p className="text-xs text-gray-500">15 min ago | Vinod</p>
                    </div>
                    <Button size="sm" variant="outline">
                      View Progress
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Global System Health */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="mr-2 h-5 w-5" />
                  Global System Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>South City Mall</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-green-600">✅ 98%</span>
                    </div>
                  </div>
                  <Progress value={98} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span>City Center Mall</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-yellow-600">⚠️ 85%</span>
                    </div>
                  </div>
                  <Progress value={85} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span>Mani Casadona IT Park</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-green-600">✅ 92%</span>
                    </div>
                  </div>
                  <Progress value={92} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span>Manipal Hospital</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-green-600">✅ 95%</span>
                    </div>
                  </div>
                  <Progress value={95} className="h-2" />
                </div>

                <div className="mt-6">
                  <h4 className="font-semibold mb-2">📈 Performance Trends</h4>
                  <div className="bg-gray-100 rounded-lg p-4 text-sm">
                    <p>• Resolution Times: Improving</p>
                    <p>• Ticket Volume: +15% this week</p>
                    <p>• Engineer Efficiency: 92% avg</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="mr-2 h-5 w-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-3">
                  <Button className="justify-start">
                    <Ticket className="mr-2 h-4 w-4" />
                    Create Ticket
                  </Button>
                  <Button className="justify-start" variant="outline">
                    <Users className="mr-2 h-4 w-4" />
                    Assign Engineer
                  </Button>
                  <Button className="justify-start" variant="outline">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Send Announcement
                  </Button>
                  <Button className="justify-start" variant="outline">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    Generate Report
                  </Button>
                  <Button className="justify-start" variant="outline">
                    <Settings className="mr-2 h-4 w-4" />
                    System Settings
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* AI Insights */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="mr-2 h-5 w-5" />
                  AI Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Peak hours: 9-11 AM
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Most issues: FASTag readers
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                    Best performer: Biplab Mandal
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                    Trend: ↗️ +15% volume this week
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                    Recommendation: Add 1 more engineer to City Center Mall
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
