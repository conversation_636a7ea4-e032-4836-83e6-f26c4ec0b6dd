"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { StatsCard } from "@/components/dashboard/stats-card"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Ticket,
  Clock,
  CheckCircle,
  Timer,
  Zap,
  Users,
  MessageSquare,
  Phone,
  BarChart3,
  Settings,
  UserPlus,
  AlertTriangle,
  MapPin,
  Calendar,
  TrendingUp,
  Activity,
  Mail,
  MoreHorizontal,
  Eye,
  Edit,
  Star,
  Building,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function PlazaManagerDashboard() {
  const [sidebarO<PERSON>, setSidebarOpen] = useState(false)

  const plazaTickets = [
    {
      id: "T-001",
      title: "FASTag Reader System Critical",
      location: "Entry Gate 1",
      priority: "urgent",
      status: "awaiting_assignment",
      reportedTime: "2 hours ago",
      description: "Complete system failure, temperature rising rapidly",
      requestedBy: "Security Team",
      estimatedTime: "2-4 hours",
    },
    {
      id: "T-002",
      title: "ANPR Camera Maintenance",
      location: "Exit Gate 2",
      priority: "medium",
      status: "in_progress",
      reportedTime: "1 day ago",
      engineer: "Mike Johnson",
      progress: 65,
      description: "Scheduled maintenance and safety inspection",
      requestedBy: "Parking Staff",
      estimatedTime: "4-6 hours",
    },
    {
      id: "T-003",
      title: "Ticket Dispenser Paper Jam",
      location: "Visitor Parking",
      priority: "high",
      status: "assigned",
      reportedTime: "3 hours ago",
      engineer: "Sarah Wilson",
      description: "Water leak detected in basement storage area",
      requestedBy: "Maintenance Team",
      estimatedTime: "1-2 hours",
    },
  ]

  const engineers = [
    {
      id: 1,
      name: "Vinod",
      avatar: "/placeholder.svg?height=40&width=40",
      status: "active",
      currentTickets: 3,
      rating: 4.8,
      phone: "******-567-8901",
      email: "<EMAIL>",
      specialties: ["FASTag", "ANPR"],
      location: "Entry Gate 1",
      eta: "15 min",
    },
    {
      id: 2,
      name: "Biplab Mandal",
      avatar: "/placeholder.svg?height=40&width=40",
      status: "available",
      currentTickets: 1,
      rating: 4.9,
      phone: "******-567-8902",
      email: "<EMAIL>",
      specialties: ["RFID", "UHF"],
      location: "Exit Gate 2",
      eta: "5 min",
    },
    {
      id: 3,
      name: "Shahid Ansari",
      avatar: "/placeholder.svg?height=40&width=40",
      status: "busy",
      currentTickets: 5,
      rating: 4.7,
      phone: "******-567-8903",
      email: "<EMAIL>",
      specialties: ["Ticket Dispensers", "PGS"],
      location: "Visitor Parking",
      eta: "30 min",
    },
  ]

  const announcements = [
    {
      id: 1,
      title: "New FASTag Protocol Effective Monday",
      content: "Updated safety procedures for all maintenance work",
      type: "important",
      date: "2024-01-15",
    },
    {
      id: 2,
      title: "ANPR Camera Upgrade Schedule Updated",
      content: "Weekly maintenance schedule has been revised",
      type: "info",
      date: "2024-01-14",
    },
    {
      id: 3,
      title: "Emergency Contact List for Parking Systems Updated",
      content: "New emergency contact numbers available",
      type: "urgent",
      date: "2024-01-13",
    },
  ]

  const emergencyContacts = [
    { name: "Security", number: "ext. 911", type: "emergency" },
    { name: "Facilities Director", number: "ext. 234", type: "management" },
    { name: "IT Support", number: "ext. 456", type: "technical" },
    { name: "Medical Emergency", number: "911", type: "emergency" },
  ]

  const plazaPerformance = {
    thisMonth: {
      resolved: 89,
      averageTime: 2.5,
      customerRating: 4.6,
      totalTickets: 156,
    },
    categories: [
      { name: "FASTag Systems", percentage: 40, count: 62 },
      { name: "ANPR Cameras", percentage: 30, count: 47 },
      { name: "Ticket Dispensers", percentage: 20, count: 31 },
      { name: "RFID/UHF Gates", percentage: 10, count: 16 },
    ],
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "border-red-200 bg-red-50"
      case "high":
        return "border-orange-200 bg-orange-50"
      case "medium":
        return "border-yellow-200 bg-yellow-50"
      case "low":
        return "border-green-200 bg-green-50"
      default:
        return "border-gray-200 bg-gray-50"
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-500 text-white"
      case "high":
        return "bg-orange-500 text-white"
      case "medium":
        return "bg-yellow-500 text-white"
      case "low":
        return "bg-green-500 text-white"
      default:
        return "bg-gray-500 text-white"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600"
      case "available":
        return "text-blue-600"
      case "busy":
        return "text-yellow-600"
      case "offline":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "awaiting_assignment":
        return "bg-red-100 text-red-800"
      case "assigned":
        return "bg-yellow-100 text-yellow-800"
      case "in_progress":
        return "bg-blue-100 text-blue-800"
      case "completed":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar role="plaza-manager" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="flex-1 flex flex-col min-w-0">
        <Header title="Plaza Manager Dashboard" onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 p-6 overflow-auto">
          {/* Plaza Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 text-white mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold flex items-center">
                  <Building className="mr-3 h-8 w-8" />
                  South City Mall - Parking Management
                </h1>
                <p className="text-blue-100 mt-1">
                  Plaza Manager: Jane Smith | Capacity: 1200 vehicles | Status: Operational
                </p>
              </div>
              <div className="flex space-x-2">
                <Button variant="secondary" className="bg-red-600 text-white hover:bg-red-700">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Emergency
                </Button>
                <Button variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
                  <Eye className="mr-2 h-4 w-4" />
                  Overview
                </Button>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <StatsCard title="My Plaza Tickets" value="34" change="📈 +5 today" changeType="positive" icon={Ticket} />
            <StatsCard title="Open Tickets" value="12" change="📊 +3 pending" changeType="positive" icon={Clock} />
            <StatsCard
              title="Done This Week"
              value="18"
              change="📈 +6 completed"
              changeType="positive"
              icon={CheckCircle}
            />
            <StatsCard
              title="Avg Resolution"
              value="2.5 days"
              change="📉 -0.2 improved"
              changeType="positive"
              icon={Timer}
            />
          </div>

          {/* Quick Actions */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="mr-2 h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-3">
                <Button className="flex items-center">
                  <Ticket className="mr-2 h-4 w-4" />
                  Create Ticket
                </Button>
                <Button variant="outline" className="flex items-center">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Request Engineer
                </Button>
                <Button variant="outline" className="flex items-center">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Generate Report
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  Plaza Settings
                </Button>
                <Button variant="outline" className="flex items-center">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Send Announcement
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* My Plaza Tickets */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Ticket className="mr-2 h-5 w-5" />
                    My Plaza Tickets
                  </div>
                  <Button variant="outline" size="sm">
                    <Eye className="mr-2 h-4 w-4" />
                    View All
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 max-h-96 overflow-y-auto">
                {plazaTickets.map((ticket) => (
                  <div key={ticket.id} className={`border rounded-lg p-4 ${getPriorityColor(ticket.priority)}`}>
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge className={`text-xs ${getPriorityBadge(ticket.priority)}`}>
                            {ticket.priority.toUpperCase()}
                          </Badge>
                          <Badge className={`text-xs ${getStatusBadge(ticket.status)}`}>
                            {ticket.status.replace("_", " ").toUpperCase()}
                          </Badge>
                        </div>
                        <h4 className="font-semibold mb-1 truncate">{ticket.title}</h4>
                        <p className="text-sm text-gray-600 truncate">Location: {ticket.location}</p>
                        <p className="text-xs text-gray-500">Reported: {ticket.reportedTime}</p>
                        <p className="text-xs text-gray-500">Requested by: {ticket.requestedBy}</p>
                        {ticket.engineer && <p className="text-xs text-gray-500">Engineer: {ticket.engineer}</p>}
                        {ticket.progress && (
                          <div className="mt-2">
                            <div className="flex items-center justify-between text-xs mb-1">
                              <span>Progress</span>
                              <span>{ticket.progress}%</span>
                            </div>
                            <Progress value={ticket.progress} className="h-2" />
                          </div>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="flex-shrink-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Ticket
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <UserPlus className="mr-2 h-4 w-4" />
                            Assign Engineer
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <MessageSquare className="mr-2 h-4 w-4" />
                            Add Comment
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <div className="flex space-x-2">
                      {ticket.status === "awaiting_assignment" && (
                        <Button size="sm" className="flex-1">
                          <UserPlus className="mr-1 h-3 w-3" />
                          Request Engineer
                        </Button>
                      )}
                      {ticket.status === "in_progress" && (
                        <Button size="sm" variant="outline" className="flex-1">
                          <Eye className="mr-1 h-3 w-3" />
                          View Progress
                        </Button>
                      )}
                      <Button size="sm" variant="outline" className="flex-1">
                        <MessageSquare className="mr-1 h-3 w-3" />
                        Update
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Assigned Engineers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Users className="mr-2 h-5 w-5" />
                    Assigned Engineers
                  </div>
                  <Button variant="outline" size="sm">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Request More
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 max-h-96 overflow-y-auto">
                {engineers.map((engineer) => (
                  <div key={engineer.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarImage src={engineer.avatar || "/placeholder.svg"} alt={engineer.name} />
                          <AvatarFallback>
                            {engineer.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="min-w-0 flex-1">
                          <h4 className="font-medium truncate">{engineer.name}</h4>
                          <p className={`text-sm font-medium ${getStatusColor(engineer.status)}`}>
                            {engineer.status === "active"
                              ? "🟢 Active"
                              : engineer.status === "available"
                                ? "🔵 Available"
                                : engineer.status === "busy"
                                  ? "🟡 Busy"
                                  : "🔴 Offline"}
                          </p>
                          <p className="text-xs text-gray-500 truncate">Location: {engineer.location}</p>
                        </div>
                      </div>
                      <div className="text-right flex-shrink-0">
                        <p className="text-sm font-medium">{engineer.currentTickets} tickets</p>
                        <div className="flex items-center">
                          <Star className="h-3 w-3 text-yellow-400 mr-1" />
                          <span className="text-sm">{engineer.rating}</span>
                        </div>
                        <p className="text-xs text-gray-500">ETA: {engineer.eta}</p>
                      </div>
                    </div>

                    <div className="mb-3">
                      <p className="text-xs text-gray-600 mb-1">Specialties:</p>
                      <div className="flex space-x-1">
                        {engineer.specialties.map((specialty, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Phone className="mr-1 h-3 w-3" />
                          Call
                        </Button>
                        <Button size="sm" variant="outline">
                          <MessageSquare className="mr-1 h-3 w-3" />
                          Message
                        </Button>
                        <Button size="sm" variant="outline">
                          <MapPin className="mr-1 h-3 w-3" />
                          Locate
                        </Button>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Profile
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Calendar className="mr-2 h-4 w-4" />
                            View Schedule
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <BarChart3 className="mr-2 h-4 w-4" />
                            Performance
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Mail className="mr-2 h-4 w-4" />
                            Send Email
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Plaza Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5" />
                    Plaza Performance
                  </div>
                  <Button variant="outline" size="sm">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Detailed Report
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{plazaPerformance.thisMonth.resolved}%</div>
                      <p className="text-sm text-gray-600">Resolution Rate</p>
                    </div>
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{plazaPerformance.thisMonth.averageTime}</div>
                      <p className="text-sm text-gray-600">Avg Days</p>
                    </div>
                  </div>

                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="flex items-center justify-center mb-2">
                      <Star className="h-5 w-5 text-yellow-400 mr-1" />
                      <span className="text-2xl font-bold text-purple-600">
                        {plazaPerformance.thisMonth.customerRating}
                      </span>
                      <span className="text-gray-600 ml-1">/5</span>
                    </div>
                    <p className="text-sm text-gray-600">Customer Rating</p>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3">Issue Categories This Month:</h4>
                    <div className="space-y-3">
                      {plazaPerformance.categories.map((category, index) => (
                        <div key={index}>
                          <div className="flex justify-between text-sm mb-1">
                            <span>{category.name}:</span>
                            <span className="font-medium">
                              {category.count} tickets ({category.percentage}%)
                            </span>
                          </div>
                          <Progress value={category.percentage} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Communication Hub */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MessageSquare className="mr-2 h-5 w-5" />
                    Communication Hub
                  </div>
                  <Button variant="outline" size="sm">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    New Announcement
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Activity className="mr-2 h-4 w-4" />
                      Recent Announcements
                    </h4>
                    <div className="space-y-3 max-h-48 overflow-y-auto">
                      {announcements.map((announcement) => (
                        <div
                          key={announcement.id}
                          className={`p-3 rounded-lg border ${
                            announcement.type === "urgent"
                              ? "bg-red-50 border-red-200"
                              : announcement.type === "important"
                                ? "bg-yellow-50 border-yellow-200"
                                : "bg-blue-50 border-blue-200"
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <h5 className="font-medium text-sm truncate">{announcement.title}</h5>
                              <p className="text-xs text-gray-600 mt-1">{announcement.content}</p>
                              <p className="text-xs text-gray-500 mt-1">{announcement.date}</p>
                            </div>
                            <Badge
                              className={`text-xs flex-shrink-0 ml-2 ${
                                announcement.type === "urgent"
                                  ? "bg-red-500"
                                  : announcement.type === "important"
                                    ? "bg-yellow-500"
                                    : "bg-blue-500"
                              }`}
                            >
                              {announcement.type}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Phone className="mr-2 h-4 w-4" />
                      Emergency Contacts
                    </h4>
                    <div className="space-y-2">
                      {emergencyContacts.map((contact, index) => (
                        <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                          <div className="flex items-center space-x-2">
                            <div
                              className={`w-2 h-2 rounded-full ${
                                contact.type === "emergency"
                                  ? "bg-red-500"
                                  : contact.type === "management"
                                    ? "bg-blue-500"
                                    : "bg-green-500"
                              }`}
                            ></div>
                            <span className="text-sm font-medium">{contact.name}:</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm">{contact.number}</span>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <Phone className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Plaza Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Building className="mr-2 h-5 w-5" />
                  Plaza Overview & Statistics
                </div>
                <Button variant="outline" size="sm">
                  <Eye className="mr-2 h-4 w-4" />
                  Full Report
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                  <div className="text-3xl font-bold text-blue-600 mb-2">156</div>
                  <p className="text-sm text-gray-600">Total Tickets This Month</p>
                  <p className="text-xs text-green-600 mt-1">+12% from last month</p>
                </div>

                <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                  <div className="text-3xl font-bold text-green-600 mb-2">139</div>
                  <p className="text-sm text-gray-600">Tickets Resolved</p>
                  <p className="text-xs text-green-600 mt-1">89% success rate</p>
                </div>

                <div className="text-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg">
                  <div className="text-3xl font-bold text-yellow-600 mb-2">8</div>
                  <p className="text-sm text-gray-600">Engineers Assigned</p>
                  <p className="text-xs text-blue-600 mt-1">3 available now</p>
                </div>

                <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                  <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
                  <p className="text-sm text-gray-600">Emergency Coverage</p>
                  <p className="text-xs text-green-600 mt-1">Always available</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
