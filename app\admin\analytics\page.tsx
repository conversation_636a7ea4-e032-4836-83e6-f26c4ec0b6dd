"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { StatsCard } from "@/components/dashboard/stats-card"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
  MapPin,
  Ticket,
  CheckCircle,
  AlertTriangle,
  Download,
  RefreshCw,
} from "lucide-react"

export default function AdminAnalyticsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [timeRange, setTimeRange] = useState("7d")

  const overviewStats = {
    totalTickets: { value: 1247, change: "+12%", trend: "up" },
    resolvedTickets: { value: 1089, change: "+8%", trend: "up" },
    avgResolutionTime: { value: "2.4 hrs", change: "-15%", trend: "down" },
    customerSatisfaction: { value: "4.6/5", change: "+3%", trend: "up" },
    activeEngineers: { value: 15, change: "0%", trend: "neutral" },
    systemUptime: { value: "99.8%", change: "+0.2%", trend: "up" },
  }

  const ticketTrends = [
    { period: "Mon", created: 45, resolved: 42, pending: 3 },
    { period: "Tue", created: 52, resolved: 48, pending: 7 },
    { period: "Wed", created: 38, resolved: 41, pending: 4 },
    { period: "Thu", created: 61, resolved: 55, pending: 10 },
    { period: "Fri", created: 48, resolved: 52, pending: 6 },
    { period: "Sat", created: 35, resolved: 38, pending: 3 },
    { period: "Sun", created: 29, resolved: 31, pending: 1 },
  ]

  const locationPerformance = [
    {
      name: "South City Mall",
      tickets: 342,
      resolved: 318,
      avgTime: "2.1 hrs",
      satisfaction: 4.7,
      efficiency: 93,
    },
    {
      name: "City Center Mall",
      tickets: 298,
      resolved: 275,
      avgTime: "2.8 hrs",
      satisfaction: 4.5,
      efficiency: 92,
    },
    {
      name: "Mani Casadona IT Park",
      tickets: 256,
      resolved: 241,
      avgTime: "2.2 hrs",
      satisfaction: 4.6,
      efficiency: 94,
    },
    {
      name: "Manipal Hospital",
      tickets: 189,
      resolved: 178,
      avgTime: "2.0 hrs",
      satisfaction: 4.8,
      efficiency: 94,
    },
  ]

  const engineerPerformance = [
    {
      name: "Vinod",
      ticketsResolved: 89,
      avgTime: "1.8 hrs",
      satisfaction: 4.8,
      efficiency: 96,
      specialties: ["FASTag", "ANPR"],
    },
    {
      name: "Biplab Mandal",
      ticketsResolved: 76,
      avgTime: "2.1 hrs",
      satisfaction: 4.7,
      efficiency: 94,
      specialties: ["RFID", "UHF"],
    },
    {
      name: "Shahid Ansari",
      ticketsResolved: 82,
      avgTime: "2.3 hrs",
      satisfaction: 4.6,
      efficiency: 92,
      specialties: ["Dispensers", "PGS"],
    },
    {
      name: "Dinesh Bar",
      ticketsResolved: 71,
      avgTime: "2.5 hrs",
      satisfaction: 4.5,
      efficiency: 90,
      specialties: ["Maintenance", "General"],
    },
  ]

  const systemMetrics = [
    { name: "FASTag System", uptime: 99.8, incidents: 2, avgResponse: "120ms" },
    { name: "ANPR System", uptime: 98.5, incidents: 5, avgResponse: "180ms" },
    { name: "Ticket Dispensers", uptime: 99.2, incidents: 3, avgResponse: "90ms" },
    { name: "RFID Gates", uptime: 99.5, incidents: 2, avgResponse: "110ms" },
    { name: "Payment Gateway", uptime: 99.9, incidents: 1, avgResponse: "85ms" },
  ]

  const categoryBreakdown = [
    { category: "Hardware Issues", count: 456, percentage: 37 },
    { category: "Software Problems", count: 298, percentage: 24 },
    { category: "Network Issues", count: 187, percentage: 15 },
    { category: "Maintenance", count: 156, percentage: 12 },
    { category: "User Support", count: 150, percentage: 12 },
  ]

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar role="admin" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="flex-1 flex flex-col min-w-0">
        <Header title="Analytics & Reports" onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 p-6 overflow-auto">
          {/* Header Section */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
              <p className="text-gray-600">Comprehensive insights and performance metrics</p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-2">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24h">Last 24 Hours</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                  <SelectItem value="90d">Last 90 Days</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" size="icon">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            <StatsCard
              title="Total Tickets"
              value={overviewStats.totalTickets.value.toLocaleString()}
              change={overviewStats.totalTickets.change}
              changeType="positive"
              icon={Ticket}
            />
            <StatsCard
              title="Resolved Tickets"
              value={overviewStats.resolvedTickets.value.toLocaleString()}
              change={overviewStats.resolvedTickets.change}
              changeType="positive"
              icon={CheckCircle}
            />
            <StatsCard
              title="Avg Resolution Time"
              value={overviewStats.avgResolutionTime.value}
              change={overviewStats.avgResolutionTime.change}
              changeType="positive"
              icon={Clock}
            />
            <StatsCard
              title="Customer Satisfaction"
              value={overviewStats.customerSatisfaction.value}
              change={overviewStats.customerSatisfaction.change}
              changeType="positive"
              icon={TrendingUp}
            />
            <StatsCard
              title="Active Engineers"
              value={overviewStats.activeEngineers.value.toString()}
              change={overviewStats.activeEngineers.change}
              changeType="neutral"
              icon={Users}
            />
            <StatsCard
              title="System Uptime"
              value={overviewStats.systemUptime.value}
              change={overviewStats.systemUptime.change}
              changeType="positive"
              icon={BarChart3}
            />
          </div>

          <Tabs defaultValue="tickets" className="space-y-6">
            <TabsList>
              <TabsTrigger value="tickets">Ticket Analytics</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="systems">System Health</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
            </TabsList>

            {/* Ticket Analytics Tab */}
            <TabsContent value="tickets" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Ticket Trends Chart */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChart3 className="mr-2 h-5 w-5" />
                      Weekly Ticket Trends
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {ticketTrends.map((day, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="font-medium">{day.period}</span>
                            <span className="text-gray-500">
                              Created: {day.created} | Resolved: {day.resolved}
                            </span>
                          </div>
                          <div className="flex space-x-1">
                            <div className="flex-1 bg-blue-100 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full"
                                style={{ width: `${(day.created / 70) * 100}%` }}
                              />
                            </div>
                            <div className="flex-1 bg-green-100 rounded-full h-2">
                              <div
                                className="bg-green-500 h-2 rounded-full"
                                style={{ width: `${(day.resolved / 70) * 100}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 flex items-center space-x-4 text-sm">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mr-2" />
                        Created
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-500 rounded-full mr-2" />
                        Resolved
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Category Breakdown */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <AlertTriangle className="mr-2 h-5 w-5" />
                      Ticket Categories
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {categoryBreakdown.map((category, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{category.category}</span>
                            <span className="text-sm text-gray-500">{category.count}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Progress value={category.percentage} className="flex-1 h-2" />
                            <span className="text-xs text-gray-500 w-10">{category.percentage}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Performance Tab */}
            <TabsContent value="performance" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Location Performance */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MapPin className="mr-2 h-5 w-5" />
                      Location Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {locationPerformance.map((location, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{location.name}</h4>
                            <Badge className="bg-green-100 text-green-800">{location.efficiency}% Efficiency</Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>Tickets: {location.tickets}</div>
                            <div>Resolved: {location.resolved}</div>
                            <div>Avg Time: {location.avgTime}</div>
                            <div>Rating: {location.satisfaction}/5</div>
                          </div>
                          <div className="mt-2">
                            <Progress value={location.efficiency} className="h-2" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Engineer Performance */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="mr-2 h-5 w-5" />
                      Engineer Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {engineerPerformance.map((engineer, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{engineer.name}</h4>
                            <Badge className="bg-blue-100 text-blue-800">{engineer.efficiency}% Efficiency</Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-2">
                            <div>Resolved: {engineer.ticketsResolved}</div>
                            <div>Avg Time: {engineer.avgTime}</div>
                            <div>Rating: {engineer.satisfaction}/5</div>
                            <div>Specialties: {engineer.specialties.join(", ")}</div>
                          </div>
                          <Progress value={engineer.efficiency} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* System Health Tab */}
            <TabsContent value="systems" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5" />
                    System Health Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {systemMetrics.map((system, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{system.name}</h4>
                          <Badge
                            className={
                              system.uptime >= 99
                                ? "bg-green-100 text-green-800"
                                : system.uptime >= 98
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                            }
                          >
                            {system.uptime}% Uptime
                          </Badge>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-sm text-gray-600 mb-2">
                          <div>Incidents: {system.incidents}</div>
                          <div>Response: {system.avgResponse}</div>
                          <div>Status: {system.uptime >= 99 ? "Healthy" : "Warning"}</div>
                        </div>
                        <Progress value={system.uptime} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Trends Tab */}
            <TabsContent value="trends" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Resolution Time Trend</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600 mb-2">-15%</div>
                      <p className="text-sm text-gray-600">Improvement over last month</p>
                      <div className="flex items-center justify-center mt-2">
                        <TrendingDown className="h-4 w-4 text-green-600 mr-1" />
                        <span className="text-sm text-green-600">Faster resolutions</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Customer Satisfaction</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600 mb-2">4.6/5</div>
                      <p className="text-sm text-gray-600">Average rating this month</p>
                      <div className="flex items-center justify-center mt-2">
                        <TrendingUp className="h-4 w-4 text-blue-600 mr-1" />
                        <span className="text-sm text-blue-600">+3% increase</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">System Reliability</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-purple-600 mb-2">99.8%</div>
                      <p className="text-sm text-gray-600">Overall uptime</p>
                      <div className="flex items-center justify-center mt-2">
                        <TrendingUp className="h-4 w-4 text-purple-600 mr-1" />
                        <span className="text-sm text-purple-600">Stable performance</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}
