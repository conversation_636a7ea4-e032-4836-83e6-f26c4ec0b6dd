"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { StatsCard } from "@/components/dashboard/stats-card"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Ticket,
  Activity,
  Clock,
  Users,
  MapPin,
  Zap,
  BarChart3,
  UserPlus,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Phone,
  Mail,
  MoreHorizontal,
  Eye,
  Edit,
  Building,
  Calendar,
  FileText,
  Settings,
  Layers,
  HardDrive,
  Cpu,
  Wifi,
  Database,
  Shield,
  Truck,
  Search,
  Filter,
  Download,
  Printer,
  ChevronDown,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"

export default function AdminDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")

  const tickets = {
    open: [
      {
        id: "T-001",
        title: "FASTag Reader Critical Failure",
        priority: "urgent",
        location: "South City Mall, Lane 3",
        time: "2 min ago",
      },
      {
        id: "T-002",
        title: "ANPR System Down",
        priority: "high",
        location: "City Center Mall, Entrance",
        time: "15 min ago",
      },
      {
        id: "T-003",
        title: "Ticket Dispenser Jam",
        priority: "high",
        location: "Mani Casadona IT Park, Booth 1",
        time: "30 min ago",
      },
      {
        id: "T-004",
        title: "RFID Gate Malfunction",
        priority: "medium",
        location: "Manipal Hospital, Exit",
        time: "1 hr ago",
      },
      {
        id: "T-005",
        title: "UHF Antenna Issue",
        priority: "low",
        location: "South City Mall, Lane 5",
        time: "2 hrs ago",
      },
    ],
    assigned: [
      { id: "T-006", title: "PGS Calibration", engineer: "Shahid Ansari", priority: "medium", time: "30 min ago" },
      {
        id: "T-007",
        title: "Preventive Maintenance Check",
        engineer: "Dinesh Bar",
        priority: "high",
        time: "1 hr ago",
      },
      { id: "T-008", title: "FASTag Software Update", engineer: "Vinod", priority: "low", time: "2 hrs ago" },
    ],
    inProgress: [
      { id: "T-009", title: "ANPR Camera Replacement", engineer: "Vinod", priority: "medium", progress: 65 },
      { id: "T-010", title: "Ticket Dispenser Service", engineer: "Shahid Ansari", priority: "high", progress: 80 },
    ],
    resolved: [
      {
        id: "T-011",
        title: "RFID Reader Repair",
        engineer: "Biplab Mandal",
        priority: "low",
        completedTime: "30 min ago",
      },
      {
        id: "T-012",
        title: "UHF System Upgrade",
        engineer: "Biplab Mandal",
        priority: "medium",
        completedTime: "1 hr ago",
      },
      {
        id: "T-013",
        title: "PGS Sensor Cleaning",
        engineer: "Shahid Ansari",
        priority: "low",
        completedTime: "2 hrs ago",
      },
    ],
  }

  const engineers = [
    {
      id: 1,
      name: "Vinod",
      avatar: undefined, // Changed from "/placeholder.svg?height=40&width=40"
      workload: 85,
      activeTickets: 8,
      status: "active",
      zone: "South City Mall",
      specialty: "FASTag, ANPR",
      phone: "+91 98765-43210",
      email: "<EMAIL>",
    },
    {
      id: 2,
      name: "Biplab Mandal",
      avatar: undefined, // Changed from "/placeholder.svg?height=40&width=40"
      workload: 70,
      activeTickets: 6,
      status: "active",
      zone: "City Center Mall",
      specialty: "RFID, UHF",
      phone: "+91 98765-43211",
      email: "<EMAIL>",
    },
    {
      id: 3,
      name: "Shahid Ansari",
      avatar: undefined, // Changed from "/placeholder.svg?height=40&width=40"
      workload: 45,
      activeTickets: 4,
      status: "active",
      zone: "Mani Casadona IT Park",
      specialty: "Ticket Dispensers, PGS",
      phone: "+91 98765-43212",
      email: "<EMAIL>",
    },
    {
      id: 4,
      name: "Dinesh Bar",
      avatar: undefined, // Changed from "/placeholder.svg?height=40&width=40"
      workload: 60,
      activeTickets: 5,
      status: "break",
      zone: "Manipal Hospital",
      specialty: "Preventive Maintenance, General",
      phone: "+91 98765-43213",
      email: "<EMAIL>",
    },
  ]

  const activities = [
    { type: "assignment", message: "Vinod assigned to ticket #T-001", time: "2 min ago", icon: UserPlus },
    { type: "completion", message: "Biplab resolved ticket #T-230", time: "5 min ago", icon: CheckCircle },
    { type: "urgent", message: "New urgent ticket from South City Mall", time: "10 min ago", icon: AlertTriangle },
    {
      type: "request",
      message: "City Center Mall Manager requested engineer",
      time: "15 min ago",
      icon: MessageSquare,
    },
    { type: "system", message: "System backup completed successfully", time: "1 hr ago", icon: Activity },
    { type: "assignment", message: "Shahid assigned to ticket #T-045", time: "1.5 hrs ago", icon: UserPlus },
    { type: "completion", message: "Dinesh completed maintenance task", time: "2 hrs ago", icon: CheckCircle },
  ]

  const locations = [
    {
      name: "South City Mall",
      type: "Mall",
      status: "normal",
      tickets: 12,
      engineers: 4,
      health: 95,
      color: "green",
      address: "375, Prince Anwar Shah Rd, Kolkata",
      capacity: 1200,
      occupancy: 78,
      revenue: "₹45,000",
      systems: ["FASTag", "ANPR", "Ticket Dispensers", "RFID"],
    },
    {
      name: "City Center Mall",
      type: "Mall",
      status: "busy",
      tickets: 18,
      engineers: 3,
      health: 78,
      color: "yellow",
      address: "DC Block, Sector 1, Kolkata",
      capacity: 800,
      occupancy: 92,
      revenue: "₹38,500",
      systems: ["FASTag", "ANPR", "UHF", "PGS"],
    },
    {
      name: "Mani Casadona IT Park",
      type: "IT Park",
      status: "normal",
      tickets: 8,
      engineers: 2,
      health: 92,
      color: "green",
      address: "EM Block, Sector V, Kolkata",
      capacity: 1500,
      occupancy: 65,
      revenue: "₹52,000",
      systems: ["FASTag", "ANPR", "RFID", "UHF"],
    },
    {
      name: "Manipal Hospital",
      type: "Healthcare",
      status: "normal",
      tickets: 10,
      engineers: 3,
      health: 88,
      color: "green",
      address: "Mullickbazar, Park Street, Kolkata",
      capacity: 600,
      occupancy: 82,
      revenue: "₹28,000",
      systems: ["FASTag", "ANPR", "Ticket Dispensers"],
    },
  ]

  const inventory = [
    { id: "INV-001", name: "FASTag Reader", type: "Hardware", available: 12, allocated: 45, onOrder: 10, minStock: 5 },
    { id: "INV-002", name: "ANPR Camera", type: "Hardware", available: 8, allocated: 32, onOrder: 5, minStock: 3 },
    { id: "INV-003", name: "Ticket Dispenser", type: "Hardware", available: 5, allocated: 28, onOrder: 8, minStock: 4 },
    { id: "INV-004", name: "RFID Scanner", type: "Hardware", available: 15, allocated: 40, onOrder: 0, minStock: 5 },
    { id: "INV-005", name: "UHF Antenna", type: "Hardware", available: 7, allocated: 22, onOrder: 5, minStock: 3 },
    { id: "INV-006", name: "PGS Sensor", type: "Hardware", available: 20, allocated: 35, onOrder: 0, minStock: 10 },
  ]

  const systemHealth = [
    { name: "FASTag System", status: "healthy", uptime: "99.8%", lastIncident: "5 days ago", responseTime: "120ms" },
    { name: "ANPR System", status: "warning", uptime: "98.2%", lastIncident: "2 days ago", responseTime: "180ms" },
    { name: "Ticket Dispensers", status: "healthy", uptime: "99.5%", lastIncident: "7 days ago", responseTime: "90ms" },
    { name: "RFID System", status: "healthy", uptime: "99.7%", lastIncident: "10 days ago", responseTime: "110ms" },
    { name: "UHF System", status: "critical", uptime: "95.3%", lastIncident: "Today", responseTime: "350ms" },
    { name: "PGS System", status: "healthy", uptime: "99.9%", lastIncident: "14 days ago", responseTime: "85ms" },
    {
      name: "Database Servers",
      status: "healthy",
      uptime: "99.99%",
      lastIncident: "30 days ago",
      responseTime: "45ms",
    },
    { name: "API Services", status: "warning", uptime: "98.7%", lastIncident: "3 days ago", responseTime: "210ms" },
  ]

  const maintenanceSchedule = [
    {
      id: "MS-001",
      location: "South City Mall",
      system: "FASTag Readers",
      type: "Preventive",
      date: "2024-06-05",
      engineer: "Vinod",
      status: "scheduled",
    },
    {
      id: "MS-002",
      location: "City Center Mall",
      system: "ANPR Cameras",
      type: "Calibration",
      date: "2024-06-07",
      engineer: "Biplab Mandal",
      status: "scheduled",
    },
    {
      id: "MS-003",
      location: "Mani Casadona IT Park",
      system: "RFID Gates",
      type: "Firmware Update",
      date: "2024-06-10",
      engineer: "Shahid Ansari",
      status: "scheduled",
    },
    {
      id: "MS-004",
      location: "Manipal Hospital",
      system: "Ticket Dispensers",
      type: "Preventive",
      date: "2024-06-12",
      engineer: "Dinesh Bar",
      status: "scheduled",
    },
    {
      id: "MS-005",
      location: "South City Mall",
      system: "UHF System",
      type: "Upgrade",
      date: "2024-06-15",
      engineer: "Biplab Mandal",
      status: "scheduled",
    },
  ]

  const revenueData = {
    today: "₹42,500",
    thisWeek: "₹2,85,000",
    thisMonth: "₹12,45,000",
    lastMonth: "₹11,20,000",
    growth: "+11.2%",
    byLocation: [
      { location: "South City Mall", amount: "₹4,50,000", percentage: 36 },
      { location: "City Center Mall", amount: "₹3,85,000", percentage: 31 },
      { location: "Mani Casadona IT Park", amount: "₹2,60,000", percentage: 21 },
      { location: "Manipal Hospital", amount: "₹1,50,000", percentage: 12 },
    ],
    byPaymentMethod: [
      { method: "FASTag", amount: "₹6,85,000", percentage: 55 },
      { method: "Credit/Debit Card", amount: "₹3,10,000", percentage: 25 },
      { method: "UPI", amount: "₹1,85,000", percentage: 15 },
      { method: "Cash", amount: "₹65,000", percentage: 5 },
    ],
    monthlyTrend: [
      { month: "Jan", amount: "₹10,25,000" },
      { month: "Feb", amount: "₹10,75,000" },
      { month: "Mar", amount: "₹11,20,000" },
      { month: "Apr", amount: "₹11,85,000" },
      { month: "May", amount: "₹12,45,000" },
    ],
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800 border-red-200"
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600"
      case "break":
        return "text-yellow-600"
      case "offline":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  const getSystemStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "bg-green-100 text-green-800 border-green-200"
      case "warning":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "critical":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar role="admin" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="flex-1 flex flex-col min-w-0">
        <Header title="Admin Control Center" onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 p-6 overflow-auto">
          {/* Emergency Alert Banner */}
          <div className="bg-gradient-to-r from-red-600 to-red-700 rounded-lg p-4 text-white mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-6 w-6" />
                <div>
                  <h3 className="font-semibold">System Alert</h3>
                  <p className="text-red-100 text-sm">3 critical ParkWiz system failures require immediate attention</p>
                </div>
              </div>
              <Button variant="secondary" className="bg-white text-red-600 hover:bg-gray-100">
                View Details
              </Button>
            </div>
          </div>

          {/* Main Tabs */}
          <Tabs defaultValue="overview" className="mb-6" onValueChange={(value) => setActiveTab(value)}>
            <TabsList className="grid grid-cols-6 mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="tickets">Tickets</TabsTrigger>
              <TabsTrigger value="locations">Locations</TabsTrigger>
              <TabsTrigger value="inventory">Inventory</TabsTrigger>
              <TabsTrigger value="system">System Health</TabsTrigger>
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview">
              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
                <StatsCard title="New Tickets" value="23" change="📈 +8 today" changeType="positive" icon={Ticket} />
                <StatsCard
                  title="In Progress"
                  value="67"
                  change="📊 +12 active"
                  changeType="positive"
                  icon={Activity}
                />
                <StatsCard
                  title="Pending Review"
                  value="8"
                  change="📉 -2 from yesterday"
                  changeType="positive"
                  icon={Clock}
                />
                <StatsCard
                  title="Engineers Online"
                  value="12/15"
                  change="80% availability"
                  changeType="neutral"
                  icon={Users}
                />
                <StatsCard
                  title="Locations Active"
                  value="4/4"
                  change="100% operational"
                  changeType="positive"
                  icon={MapPin}
                />
              </div>

              {/* Quick Actions */}
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Zap className="mr-2 h-5 w-5" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-3">
                    <Button className="flex items-center">
                      <Ticket className="mr-2 h-4 w-4" />
                      Create New Ticket
                    </Button>
                    <Button variant="outline" className="flex items-center">
                      <UserPlus className="mr-2 h-4 w-4" />
                      Assign Engineer
                    </Button>
                    <Button variant="outline" className="flex items-center">
                      <BarChart3 className="mr-2 h-4 w-4" />
                      Generate Report
                    </Button>
                    <Button variant="outline" className="flex items-center">
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Send Announcement
                    </Button>
                    <Button variant="outline" className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      Schedule Maintenance
                    </Button>
                    <Button variant="outline" className="flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      View Logs
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                {/* Enhanced Engineer Workload */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Users className="mr-2 h-5 w-5" />
                        Engineer Workload & Status
                      </div>
                      <Button variant="outline" size="sm">
                        <UserPlus className="mr-2 h-4 w-4" />
                        Add Engineer
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 max-h-96 overflow-y-auto">
                    {engineers.map((engineer) => (
                      <div key={engineer.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <Avatar>
                              <AvatarImage src={engineer.avatar || "/placeholder.svg"} alt={engineer.name} />
                              <AvatarFallback>
                                {engineer.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h4 className="font-medium">{engineer.name}</h4>
                              <p className="text-sm text-gray-600">{engineer.zone}</p>
                              <p className={`text-sm font-medium ${getStatusColor(engineer.status)}`}>
                                {engineer.status === "active"
                                  ? "🟢 Active"
                                  : engineer.status === "break"
                                    ? "🟡 On Break"
                                    : "🔴 Offline"}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{engineer.workload}% Load</p>
                            <p className="text-xs text-gray-500">{engineer.activeTickets} active tickets</p>
                          </div>
                        </div>

                        <div className="mb-3">
                          <div className="flex items-center justify-between text-sm mb-1">
                            <span>Workload</span>
                            <span>{engineer.workload}%</span>
                          </div>
                          <Progress
                            value={engineer.workload}
                            className={`h-2 ${engineer.workload > 80 ? "bg-red-100" : engineer.workload > 60 ? "bg-yellow-100" : "bg-green-100"}`}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              <Phone className="mr-1 h-3 w-3" />
                              Call
                            </Button>
                            <Button size="sm" variant="outline">
                              <Mail className="mr-1 h-3 w-3" />
                              Email
                            </Button>
                            <Button size="sm" variant="outline">
                              <MessageSquare className="mr-1 h-3 w-3" />
                              Chat
                            </Button>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View Profile
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <BarChart3 className="mr-2 h-4 w-4" />
                                Performance
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                {/* Enhanced Activity Feed */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center">
                        <MessageSquare className="mr-2 h-5 w-5" />
                        Real-time Activity Feed
                      </div>
                      <Button variant="outline" size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        View All
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {activities.map((activity, index) => {
                        const IconComponent = activity.icon
                        return (
                          <div
                            key={index}
                            className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <div
                              className={`p-2 rounded-full flex-shrink-0 ${
                                activity.type === "urgent"
                                  ? "bg-red-100 text-red-600"
                                  : activity.type === "completion"
                                    ? "bg-green-100 text-green-600"
                                    : activity.type === "assignment"
                                      ? "bg-blue-100 text-blue-600"
                                      : activity.type === "request"
                                        ? "bg-yellow-100 text-yellow-600"
                                        : "bg-gray-100 text-gray-600"
                              }`}
                            >
                              <IconComponent className="h-4 w-4" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                              <p className="text-xs text-gray-500">{activity.time}</p>
                            </div>
                            <Button variant="ghost" size="sm" className="flex-shrink-0">
                              <Eye className="h-3 w-3" />
                            </Button>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Enhanced Location Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <MapPin className="mr-2 h-5 w-5" />
                      Location Status & Performance
                    </div>
                    <Button variant="outline" size="sm">
                      <BarChart3 className="mr-2 h-4 w-4" />
                      Detailed Analytics
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {locations.map((location, index) => (
                      <div
                        key={index}
                        className={`border rounded-lg p-4 ${
                          location.color === "green"
                            ? "border-green-200 bg-green-50"
                            : location.color === "yellow"
                              ? "border-yellow-200 bg-yellow-50"
                              : "border-red-200 bg-red-50"
                        }`}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-sm truncate">{location.name}</h4>
                          <Badge
                            className={`text-xs flex-shrink-0 ${
                              location.status === "normal"
                                ? "bg-green-500"
                                : location.status === "busy"
                                  ? "bg-yellow-500"
                                  : "bg-red-500"
                            }`}
                          >
                            {location.status.toUpperCase()}
                          </Badge>
                        </div>

                        <div className="space-y-2 text-sm mb-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Active Tickets:</span>
                            <span className="font-medium">{location.tickets}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Engineers:</span>
                            <span className="font-medium">{location.engineers}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Health Score:</span>
                            <span className="font-medium">{location.health}%</span>
                          </div>
                        </div>

                        <div className="mb-3">
                          <div className="flex items-center justify-between text-xs mb-1">
                            <span>Performance</span>
                            <span>{location.health}%</span>
                          </div>
                          <Progress value={location.health} className="h-2" />
                        </div>

                        <div className="flex space-x-1">
                          <Button size="sm" variant="outline" className="flex-1 text-xs px-2">
                            <Eye className="mr-1 h-3 w-3" />
                            View
                          </Button>
                          <Button size="sm" variant="outline" className="flex-1 text-xs px-2">
                            <BarChart3 className="mr-1 h-3 w-3" />
                            Stats
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Tickets Tab */}
            <TabsContent value="tickets">
              <div className="mb-6 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold">Ticket Management</h2>
                  <Badge className="ml-2">{Object.values(tickets).flat().length} Total</Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Button>
                    <Ticket className="mr-2 h-4 w-4" />
                    New Ticket
                  </Button>
                </div>
              </div>

              <div className="mb-6 flex flex-col md:flex-row gap-4">
                <div className="flex-1 flex items-center space-x-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input placeholder="Search tickets..." className="pl-10" />
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <Filter className="mr-2 h-4 w-4" />
                        Filter
                        <ChevronDown className="ml-2 h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem>All Tickets</DropdownMenuItem>
                      <DropdownMenuItem>Open Tickets</DropdownMenuItem>
                      <DropdownMenuItem>Assigned Tickets</DropdownMenuItem>
                      <DropdownMenuItem>In Progress</DropdownMenuItem>
                      <DropdownMenuItem>Resolved</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </Button>
                  <Button variant="outline">
                    <Printer className="mr-2 h-4 w-4" />
                    Print
                  </Button>
                </div>
              </div>

              {/* Enhanced Kanban Board */}
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Ticket className="mr-2 h-5 w-5" />
                      Tickets Management Board
                    </div>
                    <Button variant="outline" size="sm">
                      <Eye className="mr-2 h-4 w-4" />
                      View All
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Open Tickets */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-red-700">Open</h3>
                        <Badge variant="destructive">{tickets.open.length}</Badge>
                      </div>
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {tickets.open.map((ticket) => (
                          <div key={ticket.id} className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate">{ticket.title}</p>
                                <p className="text-xs text-gray-600 truncate">{ticket.location}</p>
                                <p className="text-xs text-gray-500">{ticket.time}</p>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="flex-shrink-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <UserPlus className="mr-2 h-4 w-4" />
                                    Assign Engineer
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                            <Badge className={`text-xs ${getPriorityColor(ticket.priority)}`}>
                              {ticket.priority.toUpperCase()}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Assigned Tickets */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-yellow-700">Assigned</h3>
                        <Badge className="bg-yellow-500">{tickets.assigned.length}</Badge>
                      </div>
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {tickets.assigned.map((ticket) => (
                          <div key={ticket.id} className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate">{ticket.title}</p>
                                <p className="text-xs text-gray-600 truncate">Engineer: {ticket.engineer}</p>
                                <p className="text-xs text-gray-500">{ticket.time}</p>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="flex-shrink-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Progress
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <MessageSquare className="mr-2 h-4 w-4" />
                                    Contact Engineer
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                            <Badge className={`text-xs ${getPriorityColor(ticket.priority)}`}>
                              {ticket.priority.toUpperCase()}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* In Progress Tickets */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-blue-700">In Progress</h3>
                        <Badge className="bg-blue-500">{tickets.inProgress.length}</Badge>
                      </div>
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {tickets.inProgress.map((ticket) => (
                          <div key={ticket.id} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate">{ticket.title}</p>
                                <p className="text-xs text-gray-600 truncate">Engineer: {ticket.engineer}</p>
                                <div className="mt-2">
                                  <div className="flex items-center justify-between text-xs mb-1">
                                    <span>Progress</span>
                                    <span>{ticket.progress}%</span>
                                  </div>
                                  <Progress value={ticket.progress} className="h-2" />
                                </div>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="flex-shrink-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <MessageSquare className="mr-2 h-4 w-4" />
                                    Contact Engineer
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                            <Badge className={`text-xs ${getPriorityColor(ticket.priority)}`}>
                              {ticket.priority.toUpperCase()}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Resolved Tickets */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-green-700">Resolved</h3>
                        <Badge className="bg-green-500">{tickets.resolved.length}</Badge>
                      </div>
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {tickets.resolved.map((ticket) => (
                          <div key={ticket.id} className="bg-green-50 border border-green-200 rounded-lg p-3">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate">{ticket.title}</p>
                                <p className="text-xs text-gray-600 truncate">Engineer: {ticket.engineer}</p>
                                <p className="text-xs text-gray-500">Completed: {ticket.completedTime}</p>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="flex-shrink-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Report
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <BarChart3 className="mr-2 h-4 w-4" />
                                    Analytics
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                            <Badge className={`text-xs ${getPriorityColor(ticket.priority)}`}>
                              {ticket.priority.toUpperCase()}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Locations Tab */}
            <TabsContent value="locations">
              <div className="mb-6 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold">Location Management</h2>
                  <Badge className="ml-2">{locations.length} Locations</Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Button>
                    <Building className="mr-2 h-4 w-4" />
                    Add Location
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                {locations.map((location, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardContent className="p-0">
                      <div className="flex flex-col md:flex-row">
                        <div className="bg-gradient-to-br from-blue-600 to-blue-700 text-white p-6 md:w-1/4">
                          <h3 className="text-xl font-bold mb-2">{location.name}</h3>
                          <Badge className="bg-white text-blue-700 mb-4">{location.type}</Badge>
                          <p className="text-sm text-blue-100 mb-1">{location.address}</p>
                          <div className="mt-4">
                            <Badge
                              className={`${
                                location.status === "normal"
                                  ? "bg-green-500"
                                  : location.status === "busy"
                                    ? "bg-yellow-500"
                                    : "bg-red-500"
                              }`}
                            >
                              {location.status.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                        <div className="p-6 md:w-3/4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div className="space-y-2">
                              <p className="text-sm text-gray-500">Capacity</p>
                              <p className="text-xl font-bold">{location.capacity} vehicles</p>
                            </div>
                            <div className="space-y-2">
                              <p className="text-sm text-gray-500">Current Occupancy</p>
                              <p className="text-xl font-bold">{location.occupancy}%</p>
                              <Progress value={location.occupancy} className="h-2" />
                            </div>
                            <div className="space-y-2">
                              <p className="text-sm text-gray-500">Daily Revenue</p>
                              <p className="text-xl font-bold">{location.revenue}</p>
                            </div>
                          </div>

                          <div className="mb-6">
                            <p className="text-sm text-gray-500 mb-2">Installed Systems</p>
                            <div className="flex flex-wrap gap-2">
                              {location.systems.map((system, idx) => (
                                <Badge key={idx} variant="outline" className="bg-gray-100">
                                  {system}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="space-y-2">
                              <p className="text-sm text-gray-500">Health Score</p>
                              <div className="flex items-center">
                                <Progress value={location.health} className="h-2 w-32 mr-2" />
                                <span className="text-sm font-medium">{location.health}%</span>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button size="sm">
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </Button>
                              <Button size="sm" variant="outline">
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </Button>
                              <Button size="sm" variant="outline">
                                <BarChart3 className="mr-2 h-4 w-4" />
                                Analytics
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Inventory Tab */}
            <TabsContent value="inventory">
              <div className="mb-6 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold">Equipment Inventory</h2>
                  <Badge className="ml-2">{inventory.length} Items</Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Button>
                    <Layers className="mr-2 h-4 w-4" />
                    Add Inventory
                  </Button>
                </div>
              </div>

              <div className="mb-6 flex flex-col md:flex-row gap-4">
                <div className="flex-1 flex items-center space-x-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input placeholder="Search inventory..." className="pl-10" />
                  </div>
                  <Select>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="hardware">Hardware</SelectItem>
                      <SelectItem value="software">Software</SelectItem>
                      <SelectItem value="consumable">Consumable</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </Button>
                  <Button variant="outline">
                    <Truck className="mr-2 h-4 w-4" />
                    Order
                  </Button>
                </div>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Layers className="mr-2 h-5 w-5" />
                    Inventory Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <div className="grid grid-cols-7 bg-gray-100 p-4 font-medium">
                      <div>ID</div>
                      <div className="col-span-2">Item Name</div>
                      <div>Type</div>
                      <div>Available</div>
                      <div>Allocated</div>
                      <div>Actions</div>
                    </div>
                    {inventory.map((item, index) => (
                      <div
                        key={index}
                        className={`grid grid-cols-7 p-4 ${index % 2 === 0 ? "bg-white" : "bg-gray-50"} items-center`}
                      >
                        <div className="text-sm font-medium">{item.id}</div>
                        <div className="col-span-2">{item.name}</div>
                        <div>{item.type}</div>
                        <div className={`font-medium ${item.available < item.minStock ? "text-red-600" : ""}`}>
                          {item.available}
                          {item.available < item.minStock && (
                            <Badge variant="destructive" className="ml-2 text-xs">
                              Low
                            </Badge>
                          )}
                        </div>
                        <div>{item.allocated}</div>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Truck className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* System Health Tab */}
            <TabsContent value="system">
              <div className="mb-6 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold">System Health</h2>
                </div>
                <div className="flex items-center space-x-2">
                  <Button>
                    <Settings className="mr-2 h-4 w-4" />
                    System Settings
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Server Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">99.98%</div>
                    <p className="text-xs text-gray-500">Uptime last 30 days</p>
                    <div className="mt-4 space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>CPU Usage</span>
                        <span>24%</span>
                      </div>
                      <Progress value={24} className="h-1" />
                      <div className="flex items-center justify-between text-sm">
                        <span>Memory Usage</span>
                        <span>42%</span>
                      </div>
                      <Progress value={42} className="h-1" />
                      <div className="flex items-center justify-between text-sm">
                        <span>Disk Usage</span>
                        <span>68%</span>
                      </div>
                      <Progress value={68} className="h-1" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Network Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">98.5%</div>
                    <p className="text-xs text-gray-500">Network reliability</p>
                    <div className="mt-4 space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Bandwidth Usage</span>
                        <span>56%</span>
                      </div>
                      <Progress value={56} className="h-1" />
                      <div className="flex items-center justify-between text-sm">
                        <span>Latency</span>
                        <span>120ms</span>
                      </div>
                      <Progress value={40} className="h-1" />
                      <div className="flex items-center justify-between text-sm">
                        <span>Packet Loss</span>
                        <span>0.2%</span>
                      </div>
                      <Progress value={2} className="h-1" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Database Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">99.99%</div>
                    <p className="text-xs text-gray-500">Database uptime</p>
                    <div className="mt-4 space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Query Performance</span>
                        <span>45ms avg</span>
                      </div>
                      <Progress value={30} className="h-1" />
                      <div className="flex items-center justify-between text-sm">
                        <span>Storage Usage</span>
                        <span>72%</span>
                      </div>
                      <Progress value={72} className="h-1" />
                      <div className="flex items-center justify-between text-sm">
                        <span>Connections</span>
                        <span>156/500</span>
                      </div>
                      <Progress value={31} className="h-1" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="mr-2 h-5 w-5" />
                    System Components Health
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <div className="grid grid-cols-5 bg-gray-100 p-4 font-medium">
                      <div className="col-span-2">Component</div>
                      <div>Status</div>
                      <div>Uptime</div>
                      <div>Response Time</div>
                    </div>
                    {systemHealth.map((component, index) => (
                      <div
                        key={index}
                        className={`grid grid-cols-5 p-4 ${index % 2 === 0 ? "bg-white" : "bg-gray-50"} items-center`}
                      >
                        <div className="col-span-2 flex items-center">
                          {component.name === "FASTag System" ? (
                            <HardDrive className="h-4 w-4 mr-2 text-blue-600" />
                          ) : component.name === "ANPR System" ? (
                            <Cpu className="h-4 w-4 mr-2 text-purple-600" />
                          ) : component.name === "Ticket Dispensers" ? (
                            <Ticket className="h-4 w-4 mr-2 text-green-600" />
                          ) : component.name === "RFID System" ? (
                            <Wifi className="h-4 w-4 mr-2 text-yellow-600" />
                          ) : component.name === "UHF System" ? (
                            <Wifi className="h-4 w-4 mr-2 text-red-600" />
                          ) : component.name === "PGS System" ? (
                            <Layers className="h-4 w-4 mr-2 text-indigo-600" />
                          ) : component.name === "Database Servers" ? (
                            <Database className="h-4 w-4 mr-2 text-blue-600" />
                          ) : (
                            <Settings className="h-4 w-4 mr-2 text-gray-600" />
                          )}
                          {component.name}
                        </div>
                        <div>
                          <Badge className={`${getSystemStatusColor(component.status)}`}>
                            {component.status.toUpperCase()}
                          </Badge>
                        </div>
                        <div>{component.uptime}</div>
                        <div>{component.responseTime}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-5 w-5" />
                      Maintenance Schedule
                    </div>
                    <Button variant="outline" size="sm">
                      <Calendar className="mr-2 h-4 w-4" />
                      Schedule Maintenance
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <div className="grid grid-cols-6 bg-gray-100 p-4 font-medium">
                      <div>ID</div>
                      <div>Location</div>
                      <div>System</div>
                      <div>Type</div>
                      <div>Date</div>
                      <div>Engineer</div>
                    </div>
                    {maintenanceSchedule.map((maintenance, index) => (
                      <div
                        key={index}
                        className={`grid grid-cols-6 p-4 ${index % 2 === 0 ? "bg-white" : "bg-gray-50"} items-center`}
                      >
                        <div className="text-sm font-medium">{maintenance.id}</div>
                        <div>{maintenance.location}</div>
                        <div>{maintenance.system}</div>
                        <div>{maintenance.type}</div>
                        <div>{maintenance.date}</div>
                        <div>{maintenance.engineer}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Revenue Tab */}
            <TabsContent value="revenue">
              <div className="mb-6 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold">Revenue Dashboard</h2>
                </div>
                <div className="flex items-center space-x-2">
                  <Select defaultValue="thisMonth">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="thisWeek">This Week</SelectItem>
                      <SelectItem value="thisMonth">This Month</SelectItem>
                      <SelectItem value="lastMonth">Last Month</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Export Report
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{revenueData.today}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">This Week</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{revenueData.thisWeek}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">This Month</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{revenueData.thisMonth}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Growth</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">{revenueData.growth}</div>
                    <p className="text-xs text-gray-500">vs last month</p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                {/* Revenue by Location */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MapPin className="mr-2 h-5 w-5" />
                      Revenue by Location
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {revenueData.byLocation.map((location, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{location.location}</span>
                            <span className="text-sm font-bold">{location.amount}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Progress value={location.percentage} className="flex-1 h-2" />
                            <span className="text-xs text-gray-500 w-10">{location.percentage}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Revenue by Payment Method */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChart3 className="mr-2 h-5 w-5" />
                      Revenue by Payment Method
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {revenueData.byPaymentMethod.map((method, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{method.method}</span>
                            <span className="text-sm font-bold">{method.amount}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Progress value={method.percentage} className="flex-1 h-2" />
                            <span className="text-xs text-gray-500 w-10">{method.percentage}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Monthly Revenue Trend */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <BarChart3 className="mr-2 h-5 w-5" />
                      Monthly Revenue Trend
                    </div>
                    <Button variant="outline" size="sm">
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-5 gap-4">
                    {revenueData.monthlyTrend.map((month, index) => (
                      <div
                        key={index}
                        className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg"
                      >
                        <div className="text-lg font-bold text-blue-600">{month.month}</div>
                        <div className="text-sm font-medium mt-2">{month.amount}</div>
                        <div className="text-xs text-gray-500 mt-1">
                          {index > 0 && (
                            <span className="text-green-600">
                              +
                              {(
                                ((Number.parseInt(month.amount.replace(/[₹,]/g, "")) -
                                  Number.parseInt(revenueData.monthlyTrend[index - 1].amount.replace(/[₹,]/g, ""))) /
                                  Number.parseInt(revenueData.monthlyTrend[index - 1].amount.replace(/[₹,]/g, ""))) *
                                100
                              ).toFixed(1)}
                              %
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}
