"use client"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  LayoutDashboard,
  Ticket,
  BarChart3,
  Settings,
  Users,
  MapPin,
  Building,
  Wrench,
  MessageSquare,
  HelpCircle,
  X,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

interface SidebarProps {
  role: "zara" | "admin" | "plaza-manager" | "engineer" | "client"
  isOpen?: boolean
  onClose?: () => void
}

export function Sidebar({ role, isOpen = true, onClose }: SidebarProps) {
  const pathname = usePathname()

  const getNavItems = () => {
    const baseItems = [
      { icon: LayoutDashboard, label: "Overview", href: `/${role}/dashboard` },
      { icon: Ticket, label: "Tickets", href: `/${role}/tickets`, badge: 12 },
    ]

    const roleSpecificItems = {
      zara: [
        { icon: BarChart3, label: "Analytics", href: "/zara/analytics" },
        { icon: Users, label: "All Users", href: "/zara/users" },
        { icon: MapPin, label: "Plaza Locations", href: "/zara/plaza-locations" },
        { icon: Building, label: "Plaza Management", href: "/zara/plazas" },
      ],
      admin: [
        { icon: BarChart3, label: "Analytics", href: "/admin/analytics" },
        { icon: Users, label: "Users", href: "/admin/users" },
        { icon: MapPin, label: "Plaza Locations", href: "/admin/plaza-locations" },
        { icon: Building, label: "Plaza Management", href: "/admin/plazas" },
      ],
      "plaza-manager": [
        { icon: BarChart3, label: "Analytics", href: "/plaza-manager/analytics" },
        { icon: Wrench, label: "Field Engineers", href: "/plaza-manager/engineers" },
      ],
      engineer: [
        { icon: MapPin, label: "My Assignments", href: "/engineer/route" },
        { icon: Wrench, label: "Equipment", href: "/engineer/tools" },
      ],
      client: [{ icon: BarChart3, label: "Reports", href: "/client/reports" }],
    }

    const endItems = [
      { icon: Settings, label: "Settings", href: `/${role}/settings` },
      { icon: MessageSquare, label: "Support", href: `/${role}/support` },
      { icon: HelpCircle, label: "Help", href: `/${role}/help` },
    ]

    return [...baseItems, ...roleSpecificItems[role], ...endItems]
  }

  const navItems = getNavItems()

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && <div className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" onClick={onClose} />}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed left-0 top-0 z-50 h-full w-64 bg-background border-r border-border transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:z-auto",
          isOpen ? "translate-x-0" : "-translate-x-full",
        )}
      >
        <div className="flex items-center justify-between p-4 border-b border-border lg:hidden">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground text-sm font-bold">ZP</span>
            </div>
            <span className="font-semibold text-foreground">Dashboard</span>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        <nav className="p-4 space-y-2">
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-foreground mb-2">Dashboard</h2>
            <div className="w-8 h-1 bg-primary rounded"></div>
          </div>

          {navItems.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link key={item.href} href={item.href}>
                <Button
                  variant={isActive ? "default" : "ghost"}
                  className={cn("w-full justify-start", isActive && "bg-primary text-primary-foreground")}
                >
                  <item.icon className="mr-3 h-4 w-4" />
                  {item.label}
                  {item.badge && (
                    <Badge className="ml-auto" variant="secondary">
                      {item.badge}
                    </Badge>
                  )}
                </Button>
              </Link>
            )
          })}
        </nav>
      </aside>
    </>
  )
}
