"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Search, UserPlus, MapPin, Clock, Phone, Mail, MessageSquare, Wrench, AlertTriangle } from "lucide-react"
import { ZONES } from "@/lib/zones"

export default function AssignEngineerPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedTicket, setSelectedTicket] = useState<any>(null)
  const [selectedEngineer, setSelectedEngineer] = useState<any>(null)
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false)

  const openTickets = [
    {
      id: "T-001",
      title: "FASTag Reader Critical Failure",
      priority: "urgent",
      location: "South City Mall",
      zone: "EAST",
      area: "Entry Gate 1",
      reportedTime: "2 hours ago",
      estimatedTime: "2-4 hours",
      requiredSkills: ["FASTag", "Hardware"],
    },
    {
      id: "T-004",
      title: "RFID Gate Malfunction",
      priority: "high",
      location: "UB City-Bangalore",
      zone: "SOUTH",
      area: "Main Exit Gate",
      reportedTime: "1 hour ago",
      estimatedTime: "2-3 hours",
      requiredSkills: ["RFID", "Gates"],
    },
    {
      id: "T-006",
      title: "ANPR Camera Offline",
      priority: "medium",
      location: "Aero Mall- Airport-Pune",
      zone: "WEST",
      area: "Parking Level 2",
      reportedTime: "30 min ago",
      estimatedTime: "1-2 hours",
      requiredSkills: ["ANPR", "Camera"],
    },
  ]

  const engineers = [
    {
      id: 1,
      name: "Dinesh Bar",
      zone: "EAST",
      status: "available",
      currentTickets: 2,
      rating: 4.8,
      skills: ["Hardware", "FASTag", "Maintenance"],
      location: "Mani Square",
      phone: "+91 98765-43210",
      email: "<EMAIL>",
      experience: "5 years",
      completedTickets: 234,
      avgResolutionTime: "2.1 hrs",
    },
    {
      id: 2,
      name: "Ezaz Ahmed",
      zone: "EAST",
      status: "busy",
      currentTickets: 4,
      rating: 4.6,
      skills: ["Software", "Network", "ANPR"],
      location: "City Centre Haldia",
      phone: "+91 98765-43211",
      email: "<EMAIL>",
      experience: "3 years",
      completedTickets: 156,
      avgResolutionTime: "2.5 hrs",
    },
    {
      id: 3,
      name: "Sahid Ansari",
      zone: "NORTH",
      status: "available",
      currentTickets: 1,
      rating: 4.9,
      skills: ["RFID", "UHF", "Dispensers"],
      location: "DB Mall Gwalior",
      phone: "+91 98765-43212",
      email: "<EMAIL>",
      experience: "4 years",
      completedTickets: 198,
      avgResolutionTime: "1.8 hrs",
    },
    {
      id: 4,
      name: "Biplab Mandal",
      zone: "WEST",
      status: "available",
      currentTickets: 2,
      rating: 4.7,
      skills: ["Hardware", "RFID", "Gates"],
      location: "Aero Mall- Airport-Pune",
      phone: "+91 98765-43213",
      email: "<EMAIL>",
      experience: "6 years",
      completedTickets: 287,
      avgResolutionTime: "1.9 hrs",
    },
    {
      id: 5,
      name: "Vinod Kumar",
      zone: "SOUTH",
      status: "busy",
      currentTickets: 3,
      rating: 4.8,
      skills: ["FASTag", "ANPR", "Software"],
      location: "UB City-Bangalore",
      phone: "+91 98765-43214",
      email: "<EMAIL>",
      experience: "7 years",
      completedTickets: 312,
      avgResolutionTime: "1.7 hrs",
    },
    {
      id: 6,
      name: "Prasanta Rang",
      zone: "SOUTH",
      status: "available",
      currentTickets: 1,
      rating: 4.5,
      skills: ["Maintenance", "General", "PGS"],
      location: "Manipal- Udupi-Karnataka",
      phone: "+91 98765-43215",
      email: "<EMAIL>",
      experience: "4 years",
      completedTickets: 167,
      avgResolutionTime: "2.3 hrs",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800"
      case "busy":
        return "bg-yellow-100 text-yellow-800"
      case "offline":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-orange-100 text-orange-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-green-100 text-green-800"
    }
  }

  const getMatchingEngineers = (ticket: any) => {
    return engineers
      .filter((engineer) => {
        // Same zone preference
        const sameZone = engineer.zone === ticket.zone
        // Has required skills
        const hasSkills = ticket.requiredSkills.some((skill: string) =>
          engineer.skills.some((engineerSkill) => engineerSkill.toLowerCase().includes(skill.toLowerCase())),
        )
        return sameZone || hasSkills
      })
      .sort((a, b) => {
        // Prioritize: same zone > available status > rating > fewer tickets
        if (a.zone === ticket.zone && b.zone !== ticket.zone) return -1
        if (b.zone === ticket.zone && a.zone !== ticket.zone) return 1
        if (a.status === "available" && b.status !== "available") return -1
        if (b.status === "available" && a.status !== "available") return 1
        if (b.rating !== a.rating) return b.rating - a.rating
        return a.currentTickets - b.currentTickets
      })
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar role="admin" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="flex-1 flex flex-col min-w-0">
        <Header title="Assign Engineer" onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 p-6 overflow-auto">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Engineer Assignment</h1>
            <p className="text-gray-600">Assign engineers to tickets based on zone, skills, and availability</p>
          </div>

          {/* Search and Filters */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input placeholder="Search tickets or engineers..." className="pl-10" />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Zones</SelectItem>
                      <SelectItem value="EAST">East Zone</SelectItem>
                      <SelectItem value="NORTH">North Zone</SelectItem>
                      <SelectItem value="WEST">West Zone</SelectItem>
                      <SelectItem value="SOUTH">South Zone</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="busy">Busy</SelectItem>
                      <SelectItem value="offline">Offline</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Open Tickets */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="mr-2 h-5 w-5" />
                  Open Tickets Requiring Assignment
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {openTickets.map((ticket) => (
                  <div key={ticket.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-semibold">{ticket.title}</h4>
                          <Badge className={getPriorityColor(ticket.priority)}>{ticket.priority.toUpperCase()}</Badge>
                        </div>
                        <div className="space-y-1 text-sm text-gray-600">
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {ticket.location} ({ZONES[ticket.zone as keyof typeof ZONES].name})
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {ticket.reportedTime} • Est: {ticket.estimatedTime}
                          </div>
                          <div className="flex items-center">
                            <Wrench className="h-4 w-4 mr-1" />
                            Skills: {ticket.requiredSkills.join(", ")}
                          </div>
                        </div>
                      </div>
                    </div>
                    <Button
                      className="w-full"
                      onClick={() => {
                        setSelectedTicket(ticket)
                        setIsAssignDialogOpen(true)
                      }}
                    >
                      <UserPlus className="mr-2 h-4 w-4" />
                      Find & Assign Engineer
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Engineers Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <UserPlus className="mr-2 h-5 w-5" />
                  Engineers Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 max-h-96 overflow-y-auto">
                {engineers.map((engineer) => (
                  <div key={engineer.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarFallback>
                            {engineer.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h4 className="font-medium">{engineer.name}</h4>
                          <p className="text-sm text-gray-600">{ZONES[engineer.zone as keyof typeof ZONES].name}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(engineer.status)}>{engineer.status.toUpperCase()}</Badge>
                        <p className="text-xs text-gray-500 mt-1">{engineer.currentTickets} active</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                      <div>Rating: {engineer.rating}/5 ⭐</div>
                      <div>Experience: {engineer.experience}</div>
                      <div>Completed: {engineer.completedTickets}</div>
                      <div>Avg Time: {engineer.avgResolutionTime}</div>
                    </div>

                    <div className="mb-3">
                      <p className="text-xs text-gray-500 mb-1">Skills:</p>
                      <div className="flex flex-wrap gap-1">
                        {engineer.skills.map((skill, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Phone className="mr-1 h-3 w-3" />
                        Call
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Mail className="mr-1 h-3 w-3" />
                        Email
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <MessageSquare className="mr-1 h-3 w-3" />
                        Chat
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Assignment Dialog */}
          <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Assign Engineer to Ticket</DialogTitle>
                <DialogDescription>
                  {selectedTicket && `Ticket: ${selectedTicket.title} (${selectedTicket.id})`}
                </DialogDescription>
              </DialogHeader>

              {selectedTicket && (
                <div className="space-y-6">
                  {/* Ticket Details */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Ticket Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong>Location:</strong> {selectedTicket.location}
                        </div>
                        <div>
                          <strong>Zone:</strong> {ZONES[selectedTicket.zone as keyof typeof ZONES].name}
                        </div>
                        <div>
                          <strong>Priority:</strong> {selectedTicket.priority}
                        </div>
                        <div>
                          <strong>Estimated Time:</strong> {selectedTicket.estimatedTime}
                        </div>
                        <div className="col-span-2">
                          <strong>Required Skills:</strong> {selectedTicket.requiredSkills.join(", ")}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Recommended Engineers */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Recommended Engineers</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {getMatchingEngineers(selectedTicket).map((engineer, index) => (
                          <div
                            key={engineer.id}
                            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                              selectedEngineer?.id === engineer.id ? "border-blue-500 bg-blue-50" : "hover:bg-gray-50"
                            }`}
                            onClick={() => setSelectedEngineer(engineer)}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center space-x-3">
                                <Avatar>
                                  <AvatarFallback>
                                    {engineer.name
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <h4 className="font-medium">{engineer.name}</h4>
                                  <p className="text-sm text-gray-600">
                                    {ZONES[engineer.zone as keyof typeof ZONES].name}
                                  </p>
                                </div>
                                {index === 0 && <Badge className="bg-green-100 text-green-800">Best Match</Badge>}
                              </div>
                              <div className="text-right">
                                <Badge className={getStatusColor(engineer.status)}>{engineer.status}</Badge>
                                <p className="text-xs text-gray-500">{engineer.currentTickets} tickets</p>
                              </div>
                            </div>

                            <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                              <div>Rating: {engineer.rating}/5</div>
                              <div>Avg Time: {engineer.avgResolutionTime}</div>
                              <div>Zone Match: {engineer.zone === selectedTicket.zone ? "✅" : "❌"}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Assignment Notes */}
                  <div className="space-y-2">
                    <Label htmlFor="notes">Assignment Notes (Optional)</Label>
                    <Textarea
                      id="notes"
                      placeholder="Add any special instructions or notes for the engineer..."
                      className="min-h-[80px]"
                    />
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  disabled={!selectedEngineer}
                  onClick={() => {
                    // Handle assignment logic here
                    setIsAssignDialogOpen(false)
                    setSelectedTicket(null)
                    setSelectedEngineer(null)
                  }}
                >
                  Assign Engineer
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </main>
      </div>
    </div>
  )
}
