"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Plus, Clock, MapPin, User, Wrench, CheckCircle, Edit, Trash2, Download } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { ZONES, getAllLocations, getAllEngineers } from "@/lib/zones"

export default function ScheduleMaintenancePage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [selectedZone, setSelectedZone] = useState<string>("")

  const maintenanceSchedule = [
    {
      id: "MS-001",
      title: "FASTag Reader Preventive Maintenance",
      type: "Preventive",
      priority: "medium",
      location: "South City Mall",
      zone: "EAST",
      system: "FASTag Readers",
      engineer: "Dinesh Bar",
      date: "2024-06-05",
      time: "09:00",
      duration: "4 hours",
      status: "scheduled",
      description: "Quarterly maintenance check for all FASTag readers",
      checklist: ["Clean sensors", "Update firmware", "Test connectivity"],
    },
    {
      id: "MS-002",
      title: "ANPR Camera Calibration",
      type: "Calibration",
      priority: "high",
      location: "UB City-Bangalore",
      zone: "SOUTH",
      system: "ANPR Cameras",
      engineer: "Vinod Kumar",
      date: "2024-06-07",
      time: "14:00",
      duration: "3 hours",
      status: "in_progress",
      description: "Monthly calibration of ANPR camera systems",
      checklist: ["Adjust camera angles", "Update recognition database", "Test accuracy"],
    },
    {
      id: "MS-003",
      title: "RFID Gate System Upgrade",
      type: "Upgrade",
      priority: "urgent",
      location: "Aero Mall- Airport-Pune",
      zone: "WEST",
      system: "RFID Gates",
      engineer: "Biplab Mandal",
      date: "2024-06-10",
      time: "02:00",
      duration: "6 hours",
      status: "scheduled",
      description: "Firmware upgrade for enhanced security",
      checklist: ["Backup current settings", "Install new firmware", "Test all functions"],
    },
    {
      id: "MS-004",
      title: "Ticket Dispenser Service",
      type: "Service",
      priority: "low",
      location: "DB Mall Gwalior",
      zone: "NORTH",
      system: "Ticket Dispensers",
      engineer: "Sahid Ansari",
      date: "2024-06-12",
      time: "10:00",
      duration: "2 hours",
      status: "completed",
      description: "Regular service and paper refill",
      checklist: ["Refill paper", "Clean mechanisms", "Test printing quality"],
    },
  ]

  const maintenanceTypes = ["Preventive", "Corrective", "Calibration", "Upgrade", "Service", "Inspection", "Emergency"]

  const systemTypes = [
    "FASTag Readers",
    "ANPR Cameras",
    "Ticket Dispensers",
    "RFID Gates",
    "UHF Systems",
    "PGS Sensors",
    "Payment Gateway",
    "Network Infrastructure",
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800"
      case "in_progress":
        return "bg-yellow-100 text-yellow-800"
      case "completed":
        return "bg-green-100 text-green-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-orange-100 text-orange-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getZoneLocations = (zone: string) => {
    if (!zone || zone === "all") return getAllLocations()
    return ZONES[zone as keyof typeof ZONES]?.locations || []
  }

  const getZoneEngineers = (zone: string) => {
    if (!zone || zone === "all") return getAllEngineers()
    return ZONES[zone as keyof typeof ZONES]?.engineers || []
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar role="admin" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="flex-1 flex flex-col min-w-0">
        <Header title="Schedule Maintenance" onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 p-6 overflow-auto">
          {/* Header */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Maintenance Scheduler</h1>
              <p className="text-gray-600">Plan and manage preventive and corrective maintenance</p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-2">
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Schedule Maintenance
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Schedule New Maintenance</DialogTitle>
                    <DialogDescription>Create a new maintenance schedule for equipment or systems</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Maintenance Title</Label>
                        <Input id="title" placeholder="Enter maintenance title" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="type">Type</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            {maintenanceTypes.map((type) => (
                              <SelectItem key={type} value={type.toLowerCase()}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="urgent">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="system">System</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select system" />
                          </SelectTrigger>
                          <SelectContent>
                            {systemTypes.map((system) => (
                              <SelectItem key={system} value={system.toLowerCase()}>
                                {system}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="zone">Zone</Label>
                        <Select value={selectedZone} onValueChange={setSelectedZone}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select zone" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="EAST">East Zone</SelectItem>
                            <SelectItem value="NORTH">North Zone</SelectItem>
                            <SelectItem value="WEST">West Zone</SelectItem>
                            <SelectItem value="SOUTH">South Zone</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Select disabled={!selectedZone}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select location" />
                          </SelectTrigger>
                          <SelectContent>
                            {getZoneLocations(selectedZone).map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !selectedDate && "text-muted-foreground",
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar mode="single" selected={selectedDate} onSelect={setSelectedDate} initialFocus />
                          </PopoverContent>
                        </Popover>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="time">Time</Label>
                        <Input id="time" type="time" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="duration">Duration</Label>
                        <Input id="duration" placeholder="e.g., 2 hours" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="engineer">Assign Engineer</Label>
                      <Select disabled={!selectedZone}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select engineer" />
                        </SelectTrigger>
                        <SelectContent>
                          {getZoneEngineers(selectedZone).map((engineer) => (
                            <SelectItem key={engineer} value={engineer}>
                              {engineer}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Describe the maintenance work to be performed"
                        className="min-h-[80px]"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={() => setIsCreateDialogOpen(false)}>Schedule Maintenance</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export Schedule
              </Button>
            </div>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <Select defaultValue="all">
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Zones</SelectItem>
                    <SelectItem value="EAST">East Zone</SelectItem>
                    <SelectItem value="NORTH">North Zone</SelectItem>
                    <SelectItem value="WEST">West Zone</SelectItem>
                    <SelectItem value="SOUTH">South Zone</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {maintenanceTypes.map((type) => (
                      <SelectItem key={type} value={type.toLowerCase()}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Maintenance Schedule */}
          <div className="grid gap-4">
            {maintenanceSchedule.map((maintenance) => (
              <Card key={maintenance.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold">{maintenance.title}</h3>
                        <Badge className={getPriorityColor(maintenance.priority)}>
                          {maintenance.priority.toUpperCase()}
                        </Badge>
                        <Badge className={getStatusColor(maintenance.status)}>
                          {maintenance.status.replace("_", " ").toUpperCase()}
                        </Badge>
                        <Badge variant="outline">{maintenance.type}</Badge>
                      </div>

                      <p className="text-gray-600 mb-3">{maintenance.description}</p>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {maintenance.location}
                        </div>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {maintenance.engineer}
                        </div>
                        <div className="flex items-center">
                          <CalendarIcon className="h-4 w-4 mr-1" />
                          {maintenance.date} at {maintenance.time}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          Duration: {maintenance.duration}
                        </div>
                      </div>

                      <div className="mb-3">
                        <p className="text-sm font-medium text-gray-700 mb-2">System: {maintenance.system}</p>
                        <p className="text-sm text-gray-600">
                          Zone: {ZONES[maintenance.zone as keyof typeof ZONES].name}
                        </p>
                      </div>

                      {maintenance.checklist && (
                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-700 mb-2">Checklist:</p>
                          <div className="space-y-1">
                            {maintenance.checklist.map((item, index) => (
                              <div key={index} className="flex items-center text-sm text-gray-600">
                                <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                                {item}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>ID: {maintenance.id}</span>
                        <span className="flex items-center">
                          <Wrench className="h-4 w-4 mr-1" />
                          {maintenance.system}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </main>
      </div>
    </div>
  )
}
