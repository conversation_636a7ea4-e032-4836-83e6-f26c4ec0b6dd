"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { StatsCard } from "@/components/dashboard/stats-card"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Ticket,
  Clock,
  CheckCircle,
  Star,
  Zap,
  BarChart3,
  MessageSquare,
  Phone,
  FileText,
  Calendar,
} from "lucide-react"

export default function ClientDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar role="client" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="lg:ml-64">
        <Header title="Client Dashboard" onMenuClick={() => setSidebarOpen(true)} />

        <main className="p-6">
          {/* Client Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">👤 Welcome, Alex Johnson</h1>
                <p className="text-purple-100">South City Mall - Parking Customer</p>
              </div>
              <Button variant="secondary" className="bg-white text-purple-600 hover:bg-gray-100">
                🆘 Request Help
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <StatsCard title="My Tickets" value="5" change="📈 +1" changeType="positive" icon={Ticket} />
            <StatsCard title="In Progress" value="2" change="📊 Active" changeType="neutral" icon={Clock} />
            <StatsCard title="Resolved" value="3" change="✅ Done" changeType="positive" icon={CheckCircle} />
            <StatsCard title="Satisfaction" value="4.8/5" change="⭐ Rating" changeType="positive" icon={Star} />
          </div>

          {/* Quick Actions */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="mr-2 h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-3">
                <Button>
                  <Ticket className="mr-2 h-4 w-4" />
                  New Request
                </Button>
                <Button variant="outline">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact Support
                </Button>
                <Button variant="outline">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  View Reports
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* My Recent Requests */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Ticket className="mr-2 h-5 w-5" />
                  My Recent Requests
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                  <div className="flex items-start justify-between">
                    <div>
                      <Badge className="mb-2 bg-yellow-500 text-white">IN PROGRESS</Badge>
                      <h4 className="font-semibold">🚗 FASTag Not Working</h4>
                      <p className="text-sm text-gray-600">Entry Gate 1 - Payment Issue</p>
                      <p className="text-xs text-gray-500">Submitted: 2 hours ago</p>
                      <p className="text-xs text-gray-500">Engineer: Vinod</p>
                    </div>
                  </div>
                  <div className="mt-3 space-x-2">
                    <Button size="sm" variant="outline">
                      View Progress
                    </Button>
                    <Button size="sm" variant="outline">
                      <MessageSquare className="mr-1 h-3 w-3" />
                      Message
                    </Button>
                  </div>
                </div>

                <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <div className="flex items-start justify-between">
                    <div>
                      <Badge className="mb-2 bg-green-500 text-white">COMPLETED</Badge>
                      <h4 className="font-semibold">🎫 Ticket Dispenser Issue</h4>
                      <p className="text-sm text-gray-600">Visitor Parking - Hardware</p>
                      <p className="text-xs text-gray-500">Completed: Yesterday</p>
                      <p className="text-xs text-gray-500">Engineer: Shahid Ansari</p>
                    </div>
                  </div>
                  <div className="mt-3 space-x-2">
                    <Button size="sm" variant="outline">
                      Rate Service
                    </Button>
                    <Button size="sm" variant="outline">
                      <FileText className="mr-1 h-3 w-3" />
                      Report
                    </Button>
                  </div>
                </div>

                <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <div className="flex items-start justify-between">
                    <div>
                      <Badge className="mb-2 bg-blue-500 text-white">SCHEDULED</Badge>
                      <h4 className="font-semibold">🚪 Exit Gate Malfunction</h4>
                      <p className="text-sm text-gray-600">Exit Gate 2 - RFID Issue</p>
                      <p className="text-xs text-gray-500">Scheduled: Tomorrow 10 AM</p>
                      <p className="text-xs text-gray-500">Engineer: Biplab Mandal</p>
                    </div>
                  </div>
                  <div className="mt-3 space-x-2">
                    <Button size="sm" variant="outline">
                      <Calendar className="mr-1 h-3 w-3" />
                      Reschedule
                    </Button>
                    <Button size="sm" variant="outline">
                      Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Support & Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="mr-2 h-5 w-5" />
                  Support & Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-2">📞 Emergency Contacts</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between items-center">
                        <span>Security Emergency:</span>
                        <Button size="sm" variant="outline">
                          <Phone className="mr-1 h-3 w-3" />
                          911
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Parking Emergency:</span>
                        <Button size="sm" variant="outline">
                          <Phone className="mr-1 h-3 w-3" />
                          ext. 234
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Technical Support:</span>
                        <Button size="sm" variant="outline">
                          <Phone className="mr-1 h-3 w-3" />
                          ext. 456
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Customer Service:</span>
                        <Button size="sm" variant="outline">
                          <Phone className="mr-1 h-3 w-3" />
                          ext. 789
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-2">📋 Service Hours</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Emergency Services:</span>
                        <span>24/7</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Technical Support:</span>
                        <span>6 AM - 12 AM</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Customer Service:</span>
                        <span>8 AM - 10 PM</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Maintenance:</span>
                        <span>2 AM - 6 AM</span>
                      </div>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-2">📢 Recent Updates</h4>
                    <div className="space-y-2 text-sm">
                      <div className="bg-blue-50 p-2 rounded">• New mobile app for parking payments</div>
                      <div className="bg-blue-50 p-2 rounded">• FASTag system upgrade scheduled for weekend</div>
                      <div className="bg-blue-50 p-2 rounded">• Parking rates updated: ₹20/hour for mall visitors</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Service Satisfaction */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="mr-2 h-5 w-5" />
                Service Satisfaction
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">4.8/5</div>
                  <p className="text-sm text-gray-600">Overall Rating</p>
                  <div className="flex justify-center mt-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-4 w-4 ${star <= 4 ? "text-yellow-400 fill-current" : "text-gray-300"}`}
                      />
                    ))}
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">2.1</div>
                  <p className="text-sm text-gray-600">Avg Response Time (hours)</p>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">95%</div>
                  <p className="text-sm text-gray-600">Resolution Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
