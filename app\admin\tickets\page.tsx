"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Search,
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  UserPlus,
  MessageSquare,
  Clock,
  Calendar,
  MapPin,
  User,
  FileText,
  Download,
  RefreshCw,
  Archive,
  Trash2,
} from "lucide-react"

export default function AdminTicketsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedTicket, setSelectedTicket] = useState<any>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const tickets = [
    {
      id: "T-001",
      title: "FASTag Reader Critical Failure",
      description: "Complete system failure at Entry Gate 1, temperature rising rapidly. Immediate attention required.",
      priority: "urgent",
      status: "open",
      location: "South City Mall",
      area: "Entry Gate 1",
      reportedBy: "Security Team",
      assignedTo: null,
      createdAt: "2024-01-15 09:30",
      updatedAt: "2024-01-15 09:30",
      dueDate: "2024-01-15 12:00",
      category: "Hardware",
      tags: ["critical", "fastag", "hardware"],
      attachments: ["error_log.txt", "photo_1.jpg"],
      comments: 3,
      estimatedTime: "2-4 hours",
    },
    {
      id: "T-002",
      title: "ANPR Camera Maintenance",
      description: "Scheduled maintenance and calibration for ANPR camera system at Exit Gate 2.",
      priority: "medium",
      status: "in_progress",
      location: "City Center Mall",
      area: "Exit Gate 2",
      reportedBy: "Maintenance Team",
      assignedTo: "Vinod",
      createdAt: "2024-01-14 14:20",
      updatedAt: "2024-01-15 08:45",
      dueDate: "2024-01-16 17:00",
      category: "Maintenance",
      tags: ["scheduled", "anpr", "camera"],
      attachments: ["maintenance_checklist.pdf"],
      comments: 7,
      estimatedTime: "4-6 hours",
      progress: 65,
    },
    {
      id: "T-003",
      title: "Ticket Dispenser Paper Jam",
      description: "Paper jam in ticket dispenser at visitor parking booth. Customers unable to get tickets.",
      priority: "high",
      status: "assigned",
      location: "Mani Casadona IT Park",
      area: "Visitor Parking Booth 1",
      reportedBy: "Parking Staff",
      assignedTo: "Shahid Ansari",
      createdAt: "2024-01-15 11:15",
      updatedAt: "2024-01-15 11:30",
      dueDate: "2024-01-15 15:00",
      category: "Hardware",
      tags: ["dispenser", "paper-jam", "urgent"],
      attachments: [],
      comments: 2,
      estimatedTime: "1-2 hours",
    },
    {
      id: "T-004",
      title: "RFID Gate Malfunction",
      description: "RFID gate not responding to cards, causing traffic backup at hospital exit.",
      priority: "high",
      status: "open",
      location: "Manipal Hospital",
      area: "Main Exit Gate",
      reportedBy: "Hospital Security",
      assignedTo: null,
      createdAt: "2024-01-15 10:45",
      updatedAt: "2024-01-15 10:45",
      dueDate: "2024-01-15 14:00",
      category: "Hardware",
      tags: ["rfid", "gate", "traffic"],
      attachments: ["gate_status.jpg"],
      comments: 1,
      estimatedTime: "2-3 hours",
    },
    {
      id: "T-005",
      title: "Software Update - Payment Gateway",
      description: "Update payment gateway software to latest version for enhanced security.",
      priority: "low",
      status: "resolved",
      location: "All Locations",
      area: "Payment Systems",
      reportedBy: "IT Team",
      assignedTo: "Biplab Mandal",
      createdAt: "2024-01-10 09:00",
      updatedAt: "2024-01-14 16:30",
      dueDate: "2024-01-20 17:00",
      category: "Software",
      tags: ["update", "payment", "security"],
      attachments: ["update_notes.pdf", "test_results.xlsx"],
      comments: 12,
      estimatedTime: "6-8 hours",
      resolvedAt: "2024-01-14 16:30",
    },
  ]

  const engineers = [
    { id: 1, name: "Vinod", status: "active", currentTickets: 3 },
    { id: 2, name: "Biplab Mandal", status: "active", currentTickets: 2 },
    { id: 3, name: "Shahid Ansari", status: "active", currentTickets: 4 },
    { id: 4, name: "Dinesh Bar", status: "break", currentTickets: 1 },
  ]

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800 border-red-200"
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-red-100 text-red-800"
      case "assigned":
        return "bg-yellow-100 text-yellow-800"
      case "in_progress":
        return "bg-blue-100 text-blue-800"
      case "resolved":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredTickets = tickets

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar role="admin" isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="flex-1 flex flex-col min-w-0">
        <Header title="Ticket Management" onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 p-6 overflow-auto">
          {/* Header Section */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tickets</h1>
              <p className="text-gray-600">Manage and track all support tickets</p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-2">
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Ticket
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Create New Ticket</DialogTitle>
                    <DialogDescription>Fill in the details to create a new support ticket.</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Title</Label>
                        <Input id="title" placeholder="Enter ticket title" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="urgent">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="zone">Zone</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select zone first" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="EAST">East Zone</SelectItem>
                            <SelectItem value="NORTH">North Zone</SelectItem>
                            <SelectItem value="WEST">West Zone</SelectItem>
                            <SelectItem value="SOUTH">South Zone</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select location" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="south-city">South City Mall</SelectItem>
                            <SelectItem value="mani-square">Mani Square</SelectItem>
                            <SelectItem value="city-centre-haldia">City Centre Haldia</SelectItem>
                            <SelectItem value="ub-city">UB City-Bangalore</SelectItem>
                            <SelectItem value="aero-mall">Aero Mall- Airport-Pune</SelectItem>
                            <SelectItem value="db-mall">DB Mall Gwalior</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hardware">Hardware</SelectItem>
                          <SelectItem value="software">Software</SelectItem>
                          <SelectItem value="maintenance">Maintenance</SelectItem>
                          <SelectItem value="network">Network</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea id="description" placeholder="Describe the issue in detail" className="min-h-[100px]" />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={() => setIsCreateDialogOpen(false)}>Create Ticket</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>

          {/* Filters and Search */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input placeholder="Search tickets..." className="pl-10" />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="open">Open</SelectItem>
                      <SelectItem value="assigned">Assigned</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priority</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[160px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Locations</SelectItem>
                      <SelectItem value="south-city">South City Mall</SelectItem>
                      <SelectItem value="city-center">City Center Mall</SelectItem>
                      <SelectItem value="mani-casadona">Mani Casadona IT Park</SelectItem>
                      <SelectItem value="manipal">Manipal Hospital</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="icon">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tickets List */}
          <div className="grid gap-4">
            {filteredTickets.map((ticket) => (
              <Card key={ticket.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">{ticket.title}</h3>
                        <Badge className={`${getPriorityColor(ticket.priority)} text-xs`}>
                          {ticket.priority.toUpperCase()}
                        </Badge>
                        <Badge className={`${getStatusColor(ticket.status)} text-xs`}>
                          {ticket.status.replace("_", " ").toUpperCase()}
                        </Badge>
                      </div>

                      <p className="text-gray-600 mb-3 line-clamp-2">{ticket.description}</p>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {ticket.location}
                        </div>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {ticket.reportedBy}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {ticket.createdAt}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Due: {ticket.dueDate}
                        </div>
                      </div>

                      {ticket.assignedTo && (
                        <div className="flex items-center space-x-2 mb-3">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {ticket.assignedTo
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-gray-600">Assigned to {ticket.assignedTo}</span>
                        </div>
                      )}

                      {ticket.progress && (
                        <div className="mb-3">
                          <div className="flex items-center justify-between text-sm mb-1">
                            <span>Progress</span>
                            <span>{ticket.progress}%</span>
                          </div>
                          <Progress value={ticket.progress} className="h-2" />
                        </div>
                      )}

                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          {ticket.comments} comments
                        </span>
                        <span className="flex items-center">
                          <FileText className="h-4 w-4 mr-1" />
                          {ticket.attachments.length} attachments
                        </span>
                        <span>ID: {ticket.id}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <Button variant="outline" size="sm" onClick={() => setSelectedTicket(ticket)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Ticket
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <UserPlus className="mr-2 h-4 w-4" />
                            Assign Engineer
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <MessageSquare className="mr-2 h-4 w-4" />
                            Add Comment
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Archive className="mr-2 h-4 w-4" />
                            Archive
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Ticket Detail Modal */}
          {selectedTicket && (
            <Dialog open={!!selectedTicket} onOpenChange={() => setSelectedTicket(null)}>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <span>{selectedTicket.title}</span>
                    <Badge className={`${getPriorityColor(selectedTicket.priority)}`}>
                      {selectedTicket.priority.toUpperCase()}
                    </Badge>
                    <Badge className={`${getStatusColor(selectedTicket.status)}`}>
                      {selectedTicket.status.replace("_", " ").toUpperCase()}
                    </Badge>
                  </DialogTitle>
                  <DialogDescription>
                    Ticket ID: {selectedTicket.id} • Created: {selectedTicket.createdAt}
                  </DialogDescription>
                </DialogHeader>

                <div className="grid gap-6">
                  <div>
                    <h4 className="font-semibold mb-2">Description</h4>
                    <p className="text-gray-600">{selectedTicket.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Details</h4>
                      <div className="space-y-2 text-sm">
                        <div>
                          <strong>Location:</strong> {selectedTicket.location}
                        </div>
                        <div>
                          <strong>Area:</strong> {selectedTicket.area}
                        </div>
                        <div>
                          <strong>Category:</strong> {selectedTicket.category}
                        </div>
                        <div>
                          <strong>Reported by:</strong> {selectedTicket.reportedBy}
                        </div>
                        <div>
                          <strong>Estimated time:</strong> {selectedTicket.estimatedTime}
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Assignment</h4>
                      <div className="space-y-2 text-sm">
                        <div>
                          <strong>Assigned to:</strong> {selectedTicket.assignedTo || "Unassigned"}
                        </div>
                        <div>
                          <strong>Due date:</strong> {selectedTicket.dueDate}
                        </div>
                        <div>
                          <strong>Last updated:</strong> {selectedTicket.updatedAt}
                        </div>
                        {selectedTicket.resolvedAt && (
                          <div>
                            <strong>Resolved at:</strong> {selectedTicket.resolvedAt}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {selectedTicket.tags && selectedTicket.tags.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2">Tags</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedTicket.tags.map((tag: string, index: number) => (
                          <Badge key={index} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {selectedTicket.attachments && selectedTicket.attachments.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2">Attachments</h4>
                      <div className="space-y-2">
                        {selectedTicket.attachments.map((attachment: string, index: number) => (
                          <div key={index} className="flex items-center space-x-2 text-sm">
                            <FileText className="h-4 w-4" />
                            <span>{attachment}</span>
                            <Button variant="ghost" size="sm">
                              Download
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setSelectedTicket(null)}>
                    Close
                  </Button>
                  <Button>Edit Ticket</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </main>
      </div>
    </div>
  )
}
