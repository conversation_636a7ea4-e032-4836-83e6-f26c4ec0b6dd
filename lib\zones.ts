export const ZONES = {
  EAST: {
    name: "East Zone",
    engineers: ["Dinesh Bar", "E<PERSON><PERSON> Ahmed"],
    locations: [
      "Ambuja City Centre Siliguri",
      "City Centre Haldia",
      "Mani Square",
      "CCNT",
      "West Point",
      "South City Mall",
      "Junction Mall-Durgapur",
      "Vega Circle",
      "Mani Casodana",
      "City Centre One",
      "ECO Centre- Kol",
    ],
  },
  NORTH: {
    name: "North Zone",
    engineers: ["Sa<PERSON>d Ansari"],
    locations: [
      "Ambuja City Centre Raipur",
      "DB Mall Gwalior",
      "SAM Jabalpur",
      "DB City Bhopal",
      "ISBT - Jabalpur",
      "Magneto Mall- Raipur",
      "City Centre Patna",
      "Raipur City Centre Mall",
    ],
  },
  WEST: {
    name: "West Zone",
    engineers: ["Biplab Mandal", "Subha Maiti", "Gourav Shukla"],
    locations: ["Aero Mall- Airport-Pune", "Elpro-Mall"],
  },
  SOUTH: {
    name: "South Zone",
    engineers: ["Vinod Kumar", "Prasanta Rang"],
    locations: [
      "Gulbarga Darga Shrif",
      "UB City-Bangalore",
      "Manipal- Udupi-Karnataka",
      "Umiya Velocity",
      "CapitaLand Nissan",
      "Mittal Tower",
      "MANIAPAL - OLD AIRPORT ROAD",
    ],
  },
} as const

export type ZoneKey = keyof typeof ZONES
export type Zone = (typeof ZONES)[ZoneKey]

export const getAllLocations = () => {
  return Object.values(ZONES).flatMap((zone) => zone.locations)
}

export const getAllEngineers = () => {
  return Object.values(ZONES).flatMap((zone) => zone.engineers)
}

export const getZoneByLocation = (location: string): ZoneKey | null => {
  for (const [zoneKey, zone] of Object.entries(ZONES)) {
    if (zone.locations.includes(location)) {
      return zoneKey as ZoneKey
    }
  }
  return null
}

export const getZoneByEngineer = (engineer: string): ZoneKey | null => {
  for (const [zoneKey, zone] of Object.entries(ZONES)) {
    if (zone.engineers.includes(engineer)) {
      return zoneKey as ZoneKey
    }
  }
  return null
}
